# Windows EC2 instance example

## Introduction
This is an example of the Windows EC2 Instance. The purpose of this module is to help you deploy a compliant Windows EC2 instance in your AWS account.

## Pre-requisites
Before deploying this module you will need to have the following:

| | |
|------|---------|
|Terraform tool|Used to do the deployment based on the IaC code|
|AWS account|Needed to host the resources you are deploying|
|IAM user and role for deployment|Needed for authenticating with your AWS account and for deploying the resources there|

## Usage

The easiest way to leverage this example is to copy the contents of this folder to your application repository and perform the steps below.

#### (1) Update Application specific information
- Navigate to [backend.dev.conf](backend.dev.conf) and update the values with your S3 backend configuration. ( [How to create the s3 backend can be found here.](https://github.com/merck-gen/iac-shared-cf-pattern-aws-remote-state-for-terraform))
- Navigate to [terraform.dev.tfvars](terraform.dev.tfvars) and update the values for Tags, Common, and any AWS service specific values
- Review `main.tf` and update any **source** values to the latest released version of the module found in [IAC Shared Library releases](https://go.merck.com/iacreleases)

#### (2) Deployment
Run the following commands:
```
terraform init --backend-config backend.dev.conf
terraform plan --var-file terraform.dev.tfvars -out tfplan
terraform apply tfplan
```
Note that this example may create resources which can cost money (AWS Elastic IP, for example). Run `terraform destroy --var-file terraform.dev.tfvars` when you don't need these resources.

<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.2.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~> 5.0 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_aws_kms_key"></a> [aws\_kms\_key](#module\_aws\_kms\_key) | artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/kms/aws | ~> 2.4 |
| <a name="module_cloudwatch_log_group"></a> [cloudwatch\_log\_group](#module\_cloudwatch\_log\_group) | artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/cloudwatch/aws//modules/log-group | ~> 2.0 |
| <a name="module_create_tags_policy"></a> [create\_tags\_policy](#module\_create\_tags\_policy) | artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/iam/aws//modules/iam-policy | ~> 2.2 |
| <a name="module_ec2_instance"></a> [ec2\_instance](#module\_ec2\_instance) | ../../ | n/a |
| <a name="module_ec2_security_group"></a> [ec2\_security\_group](#module\_ec2\_security\_group) | artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/security-group/aws | ~> 2.5 |
| <a name="module_labels"></a> [labels](#module\_labels) | artifacts.merck.com/terraform-iac-shared__internal/labels/aws | ~> 4.0 |

## Resources

| Name | Type |
|------|------|
| [aws_ami.msd_win](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/ami) | data source |
| [aws_iam_policy_document.kms_default](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |
| [aws_partition.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/partition) | data source |
| [aws_region.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/region) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_account_no"></a> [account\_no](#input\_account\_no) | AWS account number to deploy to | `string` | n/a | yes |
| <a name="input_ami"></a> [ami](#input\_ami) | ID of AMI to use for the instance | `string` | `null` | no |
| <a name="input_availability_zone"></a> [availability\_zone](#input\_availability\_zone) | AZ to start the instance in | `string` | `null` | no |
| <a name="input_default_tags"></a> [default\_tags](#input\_default\_tags) | A map of default tags to be applied on each resource. You can get required tags [here](https://cloud.merck.com/documentation/compliance/tagging-standards/index.html) | `map(string)` | n/a | yes |
| <a name="input_deployment_role"></a> [deployment\_role](#input\_deployment\_role) | Terraform Deployment Role | `string` | n/a | yes |
| <a name="input_enable_ami_lookup"></a> [enable\_ami\_lookup](#input\_enable\_ami\_lookup) | n/a | `bool` | `true` | no |
| <a name="input_enable_volume_tags"></a> [enable\_volume\_tags](#input\_enable\_volume\_tags) | Whether to enable volume tags (if enabled it conflicts with root\_block\_device tags) | `bool` | `true` | no |
| <a name="input_iam_instance_profile"></a> [iam\_instance\_profile](#input\_iam\_instance\_profile) | IAM Instance Profile to launch the instance with. Specified as the name of the Instance Profile | `string` | n/a | yes |
| <a name="input_instance_type"></a> [instance\_type](#input\_instance\_type) | The type of instance to start | `string` | `"t3.small"` | no |
| <a name="input_key_pair_name"></a> [key\_pair\_name](#input\_key\_pair\_name) | Key name of the Key Pair to use for the instance; which can be managed using the `aws_key_pair` resource | `string` | `null` | no |
| <a name="input_kms_use_multi_region"></a> [kms\_use\_multi\_region](#input\_kms\_use\_multi\_region) | A switch to turn multi\_region on/off for kms | `bool` | `true` | no |
| <a name="input_partition"></a> [partition](#input\_partition) | Second part of aws arn. Used only in the provider configuration. | `string` | `"aws"` | no |
| <a name="input_prefix_list_ids"></a> [prefix\_list\_ids](#input\_prefix\_list\_ids) | Prefix list | `list(string)` | `null` | no |
| <a name="input_region"></a> [region](#input\_region) | AWS Region | `string` | `"us-east-1"` | no |
| <a name="input_resource_vars"></a> [resource\_vars](#input\_resource\_vars) | map of tags to pass to labels module | <pre>object({<br/>    appname     = string<br/>    region      = string<br/>    attributes  = list(string)<br/>    label_order = list(string)<br/>    environment = optional(string, "dev")<br/>  })</pre> | n/a | yes |
| <a name="input_root_block_device_volume_size"></a> [root\_block\_device\_volume\_size](#input\_root\_block\_device\_volume\_size) | Customize details about the root block device of the instance | `string` | `50` | no |
| <a name="input_subnet_id"></a> [subnet\_id](#input\_subnet\_id) | The VPC Subnet ID to launch in | `string` | `null` | no |
| <a name="input_tags"></a> [tags](#input\_tags) | map of tags to pass to labels module | `map(string)` | `{}` | no |
| <a name="input_user_data_replace_on_change"></a> [user\_data\_replace\_on\_change](#input\_user\_data\_replace\_on\_change) | When used in combination with user\_data or user\_data\_base64 will trigger a destroy and recreate when set to true. Defaults to false if not set. | `bool` | `false` | no |
| <a name="input_vpc_id"></a> [vpc\_id](#input\_vpc\_id) | VPC ID to deploy to | `string` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_ami"></a> [ami](#output\_ami) | AMI ID that was used to create the instance |
| <a name="output_arn"></a> [arn](#output\_arn) | The ARN of the instance |
| <a name="output_availability_zone"></a> [availability\_zone](#output\_availability\_zone) | The availability zone of the created instance |
| <a name="output_capacity_reservation_specification"></a> [capacity\_reservation\_specification](#output\_capacity\_reservation\_specification) | Capacity reservation specification of the instance |
| <a name="output_cloudwatch_log_group_arn"></a> [cloudwatch\_log\_group\_arn](#output\_cloudwatch\_log\_group\_arn) | ARN of Cloudwatch log group |
| <a name="output_cloudwatch_log_group_name"></a> [cloudwatch\_log\_group\_name](#output\_cloudwatch\_log\_group\_name) | Name of Cloudwatch log group |
| <a name="output_ebs_block_device"></a> [ebs\_block\_device](#output\_ebs\_block\_device) | EBS block device information |
| <a name="output_ephemeral_block_device"></a> [ephemeral\_block\_device](#output\_ephemeral\_block\_device) | Ephemeral block device information |
| <a name="output_iam_instance_profile_arn"></a> [iam\_instance\_profile\_arn](#output\_iam\_instance\_profile\_arn) | ARN assigned by AWS to the instance profile |
| <a name="output_iam_instance_profile_id"></a> [iam\_instance\_profile\_id](#output\_iam\_instance\_profile\_id) | Instance profile's ID |
| <a name="output_iam_instance_profile_unique"></a> [iam\_instance\_profile\_unique](#output\_iam\_instance\_profile\_unique) | Stable and unique string identifying the IAM instance profile |
| <a name="output_iam_role_arn"></a> [iam\_role\_arn](#output\_iam\_role\_arn) | The Amazon Resource Name (ARN) specifying the IAM role |
| <a name="output_iam_role_name"></a> [iam\_role\_name](#output\_iam\_role\_name) | The name of the IAM role |
| <a name="output_iam_role_unique_id"></a> [iam\_role\_unique\_id](#output\_iam\_role\_unique\_id) | Stable and unique string identifying the IAM role |
| <a name="output_id"></a> [id](#output\_id) | The ID of the instance |
| <a name="output_instance_state"></a> [instance\_state](#output\_instance\_state) | The state of the instance. One of: `pending`, `running`, `shutting-down`, `terminated`, `stopping`, `stopped` |
| <a name="output_ipv6_addresses"></a> [ipv6\_addresses](#output\_ipv6\_addresses) | The IPv6 address assigned to the instance, if applicable. |
| <a name="output_outpost_arn"></a> [outpost\_arn](#output\_outpost\_arn) | The ARN of the Outpost the instance is assigned to |
| <a name="output_password_data"></a> [password\_data](#output\_password\_data) | Base-64 encoded encrypted password data for the instance. Useful for getting the administrator password for instances running Microsoft Windows. This attribute is only exported if `get_password_data` is true |
| <a name="output_primary_network_interface_id"></a> [primary\_network\_interface\_id](#output\_primary\_network\_interface\_id) | The ID of the instance's primary network interface |
| <a name="output_private_dns"></a> [private\_dns](#output\_private\_dns) | The private DNS name assigned to the instance. Can only be used inside the Amazon EC2, and only available if you've enabled DNS hostnames for your VPC |
| <a name="output_private_ip"></a> [private\_ip](#output\_private\_ip) | The private IP address assigned to the instance. |
| <a name="output_public_dns"></a> [public\_dns](#output\_public\_dns) | The public DNS name assigned to the instance. For EC2-VPC, this is only available if you've enabled DNS hostnames for your VPC |
| <a name="output_public_ip"></a> [public\_ip](#output\_public\_ip) | The public IP address assigned to the instance, if applicable. NOTE: If you are using an eip with your instance, you should refer to the EIP's address directly and not use `public_ip` as this field will change after the EIP is attached |
| <a name="output_root_block_device"></a> [root\_block\_device](#output\_root\_block\_device) | Root block device information |
| <a name="output_spot_bid_status"></a> [spot\_bid\_status](#output\_spot\_bid\_status) | The current bid status of the Spot Instance Request |
| <a name="output_spot_state"></a> [spot\_state](#output\_spot\_state) | The current request state of the Spot Instance Request |
| <a name="output_tags_all"></a> [tags\_all](#output\_tags\_all) | A map of tags assigned to the resource, including those inherited from the provider default\_tags configuration block |
<!-- END_TF_DOCS -->
