# Outputs

output "s3_bucket_id" {
  description = "The name of the bucket."
  value       = module.s3_bucket[*].s3_bucket_id
}

output "s3_bucket_arn" {
  description = "The arn of the bucket."
  value       = module.s3_bucket[*].s3_bucket_arn
}

output "replica_bucket_id" {
  description = "The name of the bucket."
  value       = module.replica_bucket[*].s3_bucket_id
}

output "replica_bucket_arn" {
  description = "The arn of the bucket."
  value       = module.replica_bucket[*].s3_bucket_arn
}

output "key_arn" {
  description = "The Amazon Resource Name (ARN) of the key"
  value       = module.aws_kms_key.key_arn
}

output "key_id" {
  description = "The globally unique identifier for the key"
  value       = module.aws_kms_key.key_id
}

output "key_policy" {
  description = "The IAM resource policy set on the key"
  value       = module.aws_kms_key.key_policy
}

output "key_arn_replica" {
  description = "The Amazon Resource Name (ARN) of the key"
  value       = module.aws_kms_key_replica.key_arn
}

output "key_id_replica" {
  description = "The globally unique identifier for the key"
  value       = module.aws_kms_key_replica.key_id
}

output "key_policy_replica" {
  description = "The IAM resource policy set on the key"
  value       = module.aws_kms_key_replica.key_policy
}

