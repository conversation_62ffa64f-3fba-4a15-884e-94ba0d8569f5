data "aws_partition" "current" {}

###############################
# Labels Module               #
###############################
module "labels" {
  source  = "artifacts.merck.com/terraform-iac-shared__internal/labels/aws"
  version = "~> 4.1"
  enabled = true

  appname     = var.resource_vars.appname
  region      = var.resource_vars.region
  attributes  = var.resource_vars.attributes
  label_order = var.resource_vars.label_order
  environment = var.resource_vars.environment
}
################################
# S3 Module - Source Bucket    #
################################
#tfsec:ignore:AWS052:exp:2023-05-01
module "s3_bucket" {
  source                                   = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/s3-bucket/aws"
  version                                  = "3.3.0"
  create_bucket                            = true
  bucket                                   = "${module.labels.id}-simple-bucket"
  policy                                   = data.aws_iam_policy_document.s3_bucket_policy.json
  cors_rule                                = var.s3_bucket_cors_rule
  lifecycle_rule                           = var.s3_bucket_lifecycle_rule
  intelligent_tiering                      = var.s3_bucket_intelligent_tiering
  tags                                     = var.tags
  default_tags                             = var.default_tags
  attach_deny_incorrect_encryption_headers = var.attach_deny_incorrect_encryption_headers
  attach_deny_incorrect_kms_key_sse        = var.attach_deny_incorrect_kms_key_sse
  allowed_kms_key_arn                      = var.allowed_kms_key_arn
  attach_deny_unencrypted_object_uploads   = var.attach_deny_unencrypted_object_uploads
  versioning                               = var.s3_bucket_versioning
  attach_policy                            = var.attach_elb_log_delivery_policy || var.attach_lb_log_delivery_policy || var.attach_policy
  website                                  = var.website
  kms_key_id                               = (var.kms_key_id != "" ? var.kms_key_id : module.aws_kms_key.key_arn)
  sse_algorithm                            = var.sse_algorithm
  bucket_key_enabled                       = true
  replication_configuration                = {}
  force_destroy                            = var.force_destroy
  logging = {
    create = false
  }
}
################################
# KMS Keys and Bucket Policies #
################################

module "aws_kms_key" {
  create             = (var.kms_key_id != null ? true : false)
  source             = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/kms/aws"
  version            = "~> 2.5"
  tags               = var.tags
  default_tags       = var.default_tags
  multi_region       = var.kms_use_multi_region
  key_administrators = ["arn:${data.aws_partition.current.partition}:iam::${var.account_no}:root", "arn:${data.aws_partition.current.partition}:iam::${var.account_no}:role/${var.deployment_role}"]
}

resource "aws_iam_role" "this" {
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
    "Action": "sts:AssumeRole",
    "Principal": {
      "Service": "ec2.amazonaws.com"
    },
    "Effect": "Allow",
    "Sid": ""
    }
  ]
}
  EOF
}

data "aws_iam_policy_document" "s3_bucket_policy" {
  statement {
    principals {
      type        = "AWS"
      identifiers = [aws_iam_role.this.arn]
    }

    actions = [
      "s3:ListBucket",
    ]

    resources = [
      "arn:${data.aws_partition.current.partition}:s3:::${module.labels.id}-Testing",
    ]
  }
}
