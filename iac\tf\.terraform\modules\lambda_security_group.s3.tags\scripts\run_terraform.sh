#!/bin/bash
set -Eeuo pipefail

function cleanup() {
  exit_code=$?
  echo "Removing directory on ${run_dir}/.terraform:"
  set -x
  rm -rf "${run_dir}/.terraform"
  set +x

  echo ""
  echo "##### Finished executing $(basename "$0") on ${DOCK_CONT_NAME} #####"
}

set_environment() {
  PW=$(pwd)
  echo "[INFO] Current path is $(pwd)"
  echo "[INFO] Content:"
  ls -Al
  echo ""
  echo "contents of TF_DIR (${PW}/${TF_DIR}):"
  ls -Al "${PW}/${TF_DIR}"
  python3 --version
  aws --version
  terraform -version
  echo "trying to set git config --global..."
  git config --global --add safe.directory "${WDIR}/${DEFAULT_GIT_DIR}"
  git config --global --add safe.directory "*"
  git config --global credential.helper "store --file=${GIT_CREDS_PATH}"
  echo "<<< Done checking environment"
  # VARIABLES
  export http_proxy="${HTTP_PROXY}"
  export https_proxy="${HTTPS_PROXY}"
  export no_proxy="${NO_PROXY}"
  export TF_IN_AUTOMATION="yes"
}

aws_get_keys() {
  echo "Getting AWS secrets..."
  [ -n "${AWS_ACCESS_KEY_ID}" ] && [ "${AWS_ACCESS_KEY_ID}" != "null" ]
  DEF_AWS_ACCESS_KEY=$?
  [ -n "${AWS_SECRET_ACCESS_KEY}" ] && [ "${AWS_SECRET_ACCESS_KEY}" != "null" ]
  DEF_AWS_SECRET=$?

  echo "AWS_ACCESS_KEY_ID is $(if [ ${DEF_AWS_ACCESS_KEY} -eq 0 ]; then echo "defined: ${AWS_ACCESS_KEY_ID}"; else echo "undefined"; fi;)"
  echo "AWS_SECRET_ACCESS_KEY is $(if [ ${DEF_AWS_SECRET} -eq 0 ]; then echo "defined"; else echo "undefined"; fi;)"

  if [ ${DEF_AWS_ACCESS_KEY} -ne 0 ] || [ ${DEF_AWS_SECRET} -ne 0 ]; then
  	echo "AWS credentials not defined, exiting."
  	exit 1
  fi

  echo "AWS Identity:"
  aws sts get-caller-identity
}

set_folder_structure() {
  tf_backend_conf="-backend-config=backend.${BUILD_ENV}.conf"
  tfvars_file="-var-file=terraform.${BUILD_ENV}.tfvars"
  run_dir="${PW}/${TF_DIR}"
  echo "Terraform will run in ${run_dir}"
  cd "${run_dir}"
  echo "Content of ${run_dir}:"
  ls -A
}


terraform_init() {
  # export TF_LOG=DEBUG
  echo ""
  echo "Running terraform init..."
  echo "terraform init ${tf_backend_conf} -no-color -input=false"
  terraform init ${tf_backend_conf} -no-color -input=false
}

terraform_plan() {
  echo ""
  echo "Running terraform plan"
  echo "terraform plan ${IS_DESTROY} ${tfvars_file} ${var} -out=${TFPLAN} -no-color -input=false 2>&1"
  set +e
  tf_plan_cmd=$(terraform plan ${IS_DESTROY} ${tfvars_file} ${var} -out=${TFPLAN} -no-color -input=false 2>&1)
  ex_code=$?
  set -e
  if [[ ${ex_code} -eq 0 ]]; then
    echo "${tf_plan_cmd}"
  else
    if [[ "${tf_plan_cmd}" == *"Error acquiring the state lock"* ]]; then
      echo "[ERROR] Error acquiring the state lock occurred. "
      echo "[ERROR] ${tf_plan_cmd}"
      echo ""
      echo "${tf_plan_cmd}" | grep "ID:"
      LOCK_ID=$(echo "${tf_plan_cmd}" | grep "ID:" |  awk '{print $2}')
      echo "[INFO] Releasing lock LOCK_ID=${LOCK_ID}..."
      echo "terraform force-unlock -force ${LOCK_ID}"
      terraform force-unlock  -force "${LOCK_ID}"
      echo "[INFO] Running terraform_plan command."
      terraform plan ${IS_DESTROY} ${tfvars_file} ${var} -out=${TFPLAN} -no-color -input=false
    else
      echo "[ERROR] Message:"
      echo "[ERROR] ${tf_plan_cmd}"
      exit ${ex_code}
    fi
  fi
  echo ""
  echo "Storing tfplan ${TFPLAN} to ${WDIR}/${TFPLAN}.json..."
  echo "terraform show -no-color -json ${TFPLAN} > ${TFPLAN}.json"
  terraform show -no-color -json "${TFPLAN}" > "${TFPLAN}".json
}

terraform_apply() {
  echo ""
  echo "Running terraform apply..."
  echo "terraform apply ${IS_DESTROY} -no-color -input=false -auto-approve ${TFPLAN}"
  terraform apply ${IS_DESTROY} -no-color -input=false -auto-approve "${TFPLAN}"

  echo ""
  echo "Getting remote state and saving to tfstate.json..."
  echo "terraform state pull > tfstate.json"
  terraform state pull > tfstate.json
}

#### DEBUG

trap 'echo "Encountered an error."' ERR
trap 'echo "Cleaning up and exiting."; cleanup;' EXIT

set +u
echo "ACTION=${ACTION}"
echo "BUILD_ENV=${BUILD_ENV}"
echo "DEFAULT_GIT_DIR=${DEFAULT_GIT_DIR}"
echo "DEFAULT_TF_DIR=${DEFAULT_TF_DIR}"
echo "TF_DIR=${TF_DIR}"
echo "TFPLAN=${TFPLAN}"
echo "WDIR=${WDIR}"
echo "AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}"
set -u

#### MAIN SCRIPT
set_environment
aws_get_keys
set_folder_structure

IS_DESTROY=""
echo "[INFO] ACTION=${ACTION}"
if [ "${ACTION}" = "deploy" ]; then
  echo "######################## ACTION = deploy for ${TF_DIR} ########################"
  terraform_init
  terraform_plan
  terraform_apply
elif [ "${ACTION}" = "destroy" ]; then
  IS_DESTROY="-destroy"
  echo "######################## ACTION = destroy for ${TF_DIR} ########################"
  terraform_init
  terraform_plan
  terraform_apply
else
  echo "Action type ${ACTION} is not supported."
  exit 1
fi


echo ""
echo "Finished ${ACTION} for ${BUILD_ENV} environment"
