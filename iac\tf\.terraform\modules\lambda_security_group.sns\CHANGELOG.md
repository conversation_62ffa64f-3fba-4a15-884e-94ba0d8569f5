# Changelog
​
All notable changes to this repository will be documented in this file.
​
The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

Please use the boilerplate code below to fill in information related to your change (create a new section for each new version).

## [Unreleased]

## [[2.2.1] - 2025-01-20](https://github.com/merck-gen/iac-shared-tf-module-aws-sns-wrapper/releases/tag/2.2.1)

### Changed

- module `ext`:
  - source changed from v6.1.1 to v6.1.2, you can find the changes made to the ext module [here](https://github.com/merck-gen/iac-shared-tf-module-aws-sns-ext/compare/v6.1.1...v6.1.2)



### Added

- `create_topic_policy`: Determines whether an SNS topic policy is created.
- `enable_default_topic_policy`: Specifies whether to enable the default topic policy. Defaults to `true`

### Changed

- module `ext`:
  - source changed from v6.1.0 to v6.1.1, you can find the changes made to the ext module [here](https://github.com/merck-gen/iac-shared-tf-module-aws-sns-ext/compare/v6.1.0...v6.1.1)


## [[2.1.0] - 2024-09-09](https://github.com/merck-gen/iac-shared-tf-module-aws-sns-wrapper/releases/tag/2.1.0)

### Changed

- module `ext`:
  - source changed from v6.0.1 to v6.1.0, you can find the changes made to the ext module [here](https://github.com/merck-gen/iac-shared-tf-module-aws-sns-ext/compare/v6.0.1...v6.1.0)
- README.md now includes relevant service site link.

### Added
- Validation condition for `default_tags` variable to validate the required tags. 

## [2.0.0] - 2024-01-15

### ⚠ BREAKING CHANGES
- Add `archive_policy` and v5 upgrade

### Changed
- source for `sns-ext` from `5.4.0` to `6.0.0` in main.tf - [merged PR](https://github.com/merck-gen/iac-shared-tf-module-aws-sns-ext/pull/6)
- source for `adoption-tags-int` from `0.2.0` to `1.0.0` in consumption.tf - [changelog](https://github.com/merck-gen/iac-shared-tf-module-aws-adoption-tags-int/blob/main/CHANGELOG.md#100---2024-02-08)
- updated all dependency modules version to latest in the example
    - aws_kms_key
        - `1.0.9` to `2.1.0`  
- README.md files

### Removed
- output `kms_wrapper` due to wrong reference

## [1.3.0] - 2023-11-17

### Changed
- README.md .
- adoption tags module version to `0.2.0`
- main.tf

## [1.2.0]

### Changed
- Updated kms version to latest in the examples and modified the kms attribute accordingly.
- Updated terraform docs as per the change made.
- Updated `source` for ext module to 5.4.0

## [1.1.3] - 2023-08-21

### Added
- consumption.tf
- `tags` module

### Changed
- README.md
- main.tf
- versions.tf

## [1.1.2] - 2023-06-29

### Features

- Ensured current example is working one.
- Made the README at the root level and the example level a compliant one
- Checkov scan results validated and no fix is required.
- Updated the jenkins build URL pointing to GITHUB.
- Renamed the terraform.auto.tfvars file to terraform.dev.tfvars
- Added data protection policy in the example, inorder to make the example compliant.
- As per Merck security standards, hard coded certain values in the wrapper level.

## [1.1.1] - 2023-06-29

### Fixed
- fix missing outputs in README

## [1.1.0] - 2023-05-11
### Features
- using new version [5.3.0 of the aws-sns-ext module](https://github.com/merck-gen/iac-shared-tf-module-aws-sns-ext/releases/tag/v5.3.0)


## [1.0.0] - 2023-05-09

### Features
- Initial release of this newly created module.
