# Changelog

​
All notable changes to this repository will be documented in this file.
​
The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

Please use the boilerplate code below to fill in information related to your change (create a new section for each new version).

## [Unreleased]

## [[2.4.0] - 2025-01-10](https://github.com/merck-gen/iac-shared-tf-module-aws-ec2-instance-wrapper/releases/tag/2.4.0)


### Changed

- `inputs`:
  - `ebs_optimized`: this variable is no longer enforced to `true` as most ec2 instance types are ebs [optimized by default](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/ebs-optimized.html) and disabling this attribute for instance which is ebs optimized has no effect

## [[2.3.0] - 2025-01-06](https://github.com/merck-gen/iac-shared-tf-module-aws-ec2-instance-wrapper/releases/tag/2.3.0)

### Changed

- module `ec2_instance_wrapper`:
  - inputs:
    - `instance_type`: Change default value from `t3.micro` to `t3a.micro`.

## [[2.2.0] - 2024-11-26](https://github.com/merck-gen/iac-shared-tf-module-aws-ec2-instance-wrapper/releases/tag/2.2.0)

### Changed

- variable `ebs_volumes`:
  - `throughput`: type changed from `string` to `number`, default value changed from 200 to 125

## [[2.1.1] - 2024-10-16](https://github.com/merck-gen/iac-shared-tf-module-aws-ec2-instance-wrapper/releases/tag/2.1.1)

### Changed

- module `ec2_instance_wrapper`:
  - source changed from v5.7.0 to v5.7.1, you can find the changes made to the ext module [here](https://github.com/merck-gen/iac-shared-tf-module-aws-ec2-instance-ext/compare/v5.7.0...v5.7.1)

### Fixed

- create `local_file` for `wait_for_instance` only when this feature is enabled

## [[2.1.0] - 2024-09-12](https://github.com/merck-gen/iac-shared-tf-module-aws-ec2-instance-wrapper/releases/tag/2.1.0)

### Added

- module `ec2_instance_wrapper`:
  - inputs:
    - `create_eip`: (Optional) Determines whether a public EIP will be created and associated with the instance.
    - `eip_domain`: (Optional) Indicates if this EIP is for use in VPC
    - `eip_tags`: (Optional) A map of additional tags to add to the eip

### Changed

- module `ec2_instance_wrapper`:
  - source changed from v5.6.1-repackaged to v5.7.0, you can find the changes made to the ext module [here](https://github.com/merck-gen/iac-shared-tf-module-aws-ec2-instance-ext/compare/v5.6.1-repackaged...v5.7.0)
  - outputs:
    - `public_ip`:
      - value changed from `${try(aws_instance.this[0].public_ip, aws_instance.ignore_ami[0].public_ip, aws_spot_instance_request.this[0].public_ip, None)}` to `${try(aws_eip.this[0].public_ip, aws_instance.this[0].public_ip, aws_instance.ignore_ami[0].public_ip, aws_spot_instance_request.this[0].public_ip, None)}

### Changed

- pipeline url from jenkins to GitHub Actions in README.md

## [2.0.1] - 2024-07-30

### Added

- attributes to the variable `ebs_volumes`:
  - `delete_on_termination`
  - `iops`
  - `snapshot_id`
  - `tags`

## [2.0.0] - 2024-07-25

### Added

- - `.config.yaml` file for module generator.
- Ouputs
  - `spot_instance_id`: The Instance ID (if any) that is currently fulfilling the Spot Instance request
- Validation condition for `default_tags` variable to validate the required tags.

### Changed

- Output name
  - `spot_state` -> `spot_request_state`

## [1.9.2-1.9.3] - 2024-06-11

- released only to repackage the module so it doesn't contain examples folder

## [1.9.1] - 2024-05-21

### Added

- input variables:
  - `root_block_device_deletion_on_termination`: Whether the volume should be destroyed on instance termination
  - `root_block_device_iops`: Amount of provisioned IOPS
  - `root_block_device_kms`: Amazon Resource Name (ARN) of the KMS Key to use when encrypting the volume

### Changed

- README

## [1.9.0] - 2024-04-09

### Added

- `wait_for_instance`
  - Use this variable to configure [wait.sh.tpl](wait.sh.tpl) script. If you want to use custom script, please know that INSTANCE_ID, SLEEP_TIME, NUM_OF_CYCLES, FAILED_VALUE, TAG_NAME and DEPLOYMENT_ROLE_ARN are template variables and are going to be passed into your script, which you should use.

## [1.8.0] - 2024-04-03

### Added

- `enable_coreinfra_backup` and `enable_coreinfra_backup_copy_to_bunker` variables added in variables.tf
- `core_infra_tags` local parameter for core-infra-backup tagging provision added in root main.tf

## [1.7.1] - 2024-03-27

### Changed

- source for `ec2-instance-ext` from `5.6.0` to `5.6.1` in main.tf - [Ext PR](https://github.com/merck-gen/iac-shared-tf-module-aws-ec2-instance-ext/pull/9)

  - Note: this increase in `ec2-instance-ext` does not qualify for a minor version increase in this wrapper since new functionality was not introduced that affects this repo

- README file now has relevant service site link included.

## [1.7.0] - 2024-01-29

### Changed

- source for `ec2-instance-ext` from `5.5.0` to `5.6.0` in main.tf - [Ext PR](https://github.com/merck-gen/iac-shared-tf-module-aws-ec2-instance-ext/pull/8)
- README.md

### Added

- Variable `private_dns_name_options`

## [1.6.0] - 2024-01-24

### Changed

- source for `adoption-tags-int` from `0.2.0` to `1.0.0` in consumption.tf - [Adoption Tag Changelog](https://github.com/merck-gen/iac-shared-tf-module-aws-adoption-tags-int/blob/main/CHANGELOG.md#100---2024-02-08)
- updated all dependency modules version to latest in the example.

  - cloudwatch_log_group
    - '1.0.3' to '1.3.0'
  - aws_kms_key
    - '1.0.9' to '2.1.0'
  - ec2_security_group
    - '2.0.1' to '2.1.0'

- README.md in examples folder.

## [1.5.0] - 2023-11-30

### Changed

- README.md `examples` and `module` section.
- adoption tags module version to `0.2.0`
- labels module version to `3.0.0`
- Updated variables.

## [1.4.1] - 2023-10-16

### Changed

- README.md in examples folder
- `iac-shared-tf-module-aws-adoption-tags-int` to version `0.1.1` in consumption.tf

## [1.4.0] - 2023-09-14

### Changed

- `source` version updated to `5.5.0`
- Updated terraform version to `1.2.0` and aws provider version to `5.0.1`
- Updated output variables
- Replaced cloudwatch resource block with module call in the example.
- Updated version file.
- Updated changelog.
- Updated README.

### Added

- Added new output variables.

## [1.3.0] - 2023-08-21

### Changed

- `source` version updated to `5.3.1`
- README.md

## [1.2.1] - 2023-08-09

### Added

- consumption.tf
- tags module

### Changed

- README.md
- examples/pattern_examples/README.md
- version.tf terraform version '>=1.2.0'
- examples/pattern_examples/README.md/version.tf terraform version '>=1.2.0'

## [1.2.0] - 2023-08-02

### Added

- New argument instance_tags added to the wrapper that is available in the latest ext version.

### Changed

- Updated the source for this wrapper, `iac-shared-tf-module-aws-ec2-instance-ext` version from 5.1.0 to 5.2.1.
- Regenerated the TF docs.
- Updated the module call for security group in the example section to use the latest released version of the wrapper.

## [1.1.0] - 2023-07-07

### Added

- New arguments , cpu_options and ignore_ami_changes , added to the wrapper that is available in the latest ext version.

### Changed

- Updated the source for this wrapper, iac-shared-tf-module-aws-ec2-instance-ext version from 4.3.0 to 5.1.0 .
- Regenerated the TF docs.
- Updated the module calls in the example section to use the latest released version of the wrapper for the corresponding service.

## [1.0.5] - 2023-06-30

### Changed

- Changelog updates

## [1.0.4] - 2023-06-29

### Changed

- Build URL
- Updated README to match the standard item.
- Worked on the wrapper main.tf to deal with the naming of the instance issue where the passed values weren't taken in.
- Updated the versions.tf file to use latest version.

### Fixed

- Worked on the Checkov failed checks and changes have been made to incorporate the solution for those.
