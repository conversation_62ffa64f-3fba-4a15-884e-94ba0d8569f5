#  S3 Bucket Wrapper Module

[![CI Pipeline](https://github.com/merck-gen/iac-shared-tf-module-aws-s3-bucket-wrapper/actions/workflows/branch-ci.yaml/badge.svg)](https://github.com/merck-gen/iac-shared-tf-module-aws-s3-bucket-wrapper/actions/workflows/branch-ci.yaml) [![Terratest](https://github.com/merck-gen/iac-shared-tf-module-aws-s3-bucket-wrapper/actions/workflows/terratest.yaml/badge.svg)](https://github.com/merck-gen/iac-shared-tf-module-aws-s3-bucket-wrapper/actions/workflows/terratest.yaml) 

## Table of Contents

- [Introduction](#introduction)
- [Prerequisites](#prerequisites)
- [Getting started](#getting-started)
- [Usage](#usage)
- [Useful links](#useful-links)
- [Contributing](#contributing)
- [Contact](#contact)
- [Inputs](#Inputs)
- [Outputs](#Outputs)

## Introduction

This is the Merck wrapper for [S3 bucket community module](https://github.com/merck-gen/iac-shared-tf-module-aws-s3-bucket-ext). The purpose of this module is to help you deploy a compliant [S3 bucket](https://cloud.merck.com/documentation/native-services/aws/s3/index.html) in your AWS account. A separate bucket is optionally created to enable logging if the user doesn't provide a pre-existing bucket to be used.

## Prerequisites

Before deploying this module you will need to have the following:

|                                |                                                                                                                                                                                                  |
| ------------------------------ | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| Terraform tool                 | Used to do the deployment based on the IaC code. You can install terraform from the Self-Service (Mac) or Software Center (PC).                                                                  |
| AWS account                    | Needed to host the resources you are deploying. You can get one [here](https://cloud.merck.com/support/kb/aws/request-a-dedicated-aws-account/index.html).                                       |
| Terraform backend and IAM role | If you don't have an s3 bucket, dynamodb table and IAM deployment role created, follow these steps [here](https://github.com/merck-gen/iac-cicd-terraform-template/tree/main/iac/prerequisites). |
|Deployment permissions|The needed IAM permissions for deploying this module can be found from [iam](iam/) folder in root level or inside examples folder.|

> [!TIP]
> See the [releases](https://github.com/merck-gen/iac-shared-tf-module-aws-s3-bucket-wrapper/releases) page to see how to reference this module!

## Getting started

We've prepared an [user training](https://github.com/merck-gen/iac-shared-training-user) documentation which will help you get started.

## Usage

Check out [Shorts](#shorts) section or [releases](https://github.com/merck-gen/iac-shared-tf-module-aws-s3-bucket-wrapper/releases) page to see how to reference the module.

### Examples

You can see how the module is used in the [examples](examples/) folder or in the [Shorts](#shorts) section in this readme.

### Shorts

```hcl
module "s3_bucket" {
  source                    = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/s3-bucket/aws"
  version                   = "~> 3.2"
  create_bucket             = true
  bucket                    = "simple-bucket"
  bucket_key_enabled        = true
  replication_configuration = {}
  force_destroy             = true
  s3_log_bucket             = "simple-log-bucket"
  s3_log_force_destroy      = true
  default_tags = {
    Application        = "myapp"
    Consumer           = "<EMAIL>"
    Environment        = "Development"
    DataClassification = "Proprietary"
    Service            = "some-service"
  }
}
```

### Other

| :warning:  Note!        |
|:---------------------------|
| If you want to use your own logging bucket, set `create_logging_bucket` to `false`. |

## Useful links

- S3 Bucket service [cloud.merck.com page](https://cloud.merck.com/documentation/native-services/aws/s3/).
- A [how to start with cloud page](https://cloud.merck.com/introduction/how-to-start/index.html).
- [IaC Share page](https://go/iac).
- [Infrastructure as a Code practicioners](https://teams.microsoft.com/l/channel/19%3acc5325594d1543069ea7270bcd393da1%40thread.skype/Infrastructure%2520as%2520Code%2520Practitioners?groupId=7d021e32-5295-41c2-ad7c-24f0f6de8ff4&tenantId=a00de4ec-48a8-43a6-be74-e31274e2060d) MS Teams channel.
- [How to create ticket for IaC team](https://share.merck.com/display/IACS/IAC+Shared+-+Contact+Us).
- [Cost optimization Cloud Services documentation](https://cloud.merck.com/documentation/finance/cost-optimization/index.html) to learn how to optimize S3 infrastructure and other AWS services.

## Contributing

Everyone is welcome to contribute to the code of this module! Please see the [instructions on how to contribute here](https://github.com/merck-gen/iac-shared-training-user/tree/main/contributing-guide).

## Contact

In case any issues regarding the module please comment on the [MS Teams Infrastructure as Code channel](https://teams.microsoft.com/l/channel/19%3acc5325594d1543069ea7270bcd393da1%40thread.skype/Infrastructure%2520as%2520Code?groupId=7d021e32-5295-41c2-ad7c-24f0f6de8ff4&tenantId=a00de4ec-48a8-43a6-be74-e31274e2060d) or connect via email <<EMAIL>>.

<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | >= 5.27 |
| <a name="requirement_local"></a> [local](#requirement\_local) | >= 2.4.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_local"></a> [local](#provider\_local) | >= 2.4.0 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_s3_bucket_wrapper"></a> [s3\_bucket\_wrapper](#module\_s3\_bucket\_wrapper) | artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/s3-bucket/aws | 4.2.2 |
| <a name="module_s3_log_bucket"></a> [s3\_log\_bucket](#module\_s3\_log\_bucket) | artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/s3-bucket/aws | 4.2.2 |
| <a name="module_tags"></a> [tags](#module\_tags) | https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz | n/a |

## Resources

| Name | Type |
|------|------|
| [local_file.version](https://registry.terraform.io/providers/hashicorp/local/latest/docs/data-sources/file) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_acceleration_status"></a> [acceleration\_status](#input\_acceleration\_status) | (Optional) Sets the accelerate configuration of an existing bucket. Can be Enabled or Suspended. | `string` | `null` | no |
| <a name="input_access_log_delivery_policy_source_accounts"></a> [access\_log\_delivery\_policy\_source\_accounts](#input\_access\_log\_delivery\_policy\_source\_accounts) | (Optional) List of AWS Account IDs should be allowed to deliver access logs to this bucket. | `list(string)` | `[]` | no |
| <a name="input_access_log_delivery_policy_source_buckets"></a> [access\_log\_delivery\_policy\_source\_buckets](#input\_access\_log\_delivery\_policy\_source\_buckets) | (Optional) List of S3 bucket ARNs which should be allowed to deliver access logs to this bucket. | `list(string)` | `[]` | no |
| <a name="input_acl"></a> [acl](#input\_acl) | (Optional) The canned ACL to apply. Conflicts with `grant` | `string` | `null` | no |
| <a name="input_allowed_kms_key_arn"></a> [allowed\_kms\_key\_arn](#input\_allowed\_kms\_key\_arn) | The ARN of KMS key which should be allowed in PutObject | `string` | `null` | no |
| <a name="input_analytics_configuration"></a> [analytics\_configuration](#input\_analytics\_configuration) | Map containing bucket analytics configuration. | `any` | `{}` | no |
| <a name="input_analytics_self_source_destination"></a> [analytics\_self\_source\_destination](#input\_analytics\_self\_source\_destination) | Whether or not the analytics source bucket is also the destination bucket. | `bool` | `false` | no |
| <a name="input_analytics_source_account_id"></a> [analytics\_source\_account\_id](#input\_analytics\_source\_account\_id) | The analytics source account id. | `string` | `null` | no |
| <a name="input_analytics_source_bucket_arn"></a> [analytics\_source\_bucket\_arn](#input\_analytics\_source\_bucket\_arn) | The analytics source bucket ARN. | `string` | `null` | no |
| <a name="input_attach_access_log_delivery_policy"></a> [attach\_access\_log\_delivery\_policy](#input\_attach\_access\_log\_delivery\_policy) | Controls if S3 bucket should have S3 access log delivery policy attached | `bool` | `false` | no |
| <a name="input_attach_analytics_destination_policy"></a> [attach\_analytics\_destination\_policy](#input\_attach\_analytics\_destination\_policy) | Controls if S3 bucket should have bucket analytics destination policy attached. | `bool` | `false` | no |
| <a name="input_attach_deny_incorrect_encryption_headers"></a> [attach\_deny\_incorrect\_encryption\_headers](#input\_attach\_deny\_incorrect\_encryption\_headers) | Controls if S3 bucket should deny incorrect encryption headers policy attached. | `bool` | `false` | no |
| <a name="input_attach_deny_incorrect_kms_key_sse"></a> [attach\_deny\_incorrect\_kms\_key\_sse](#input\_attach\_deny\_incorrect\_kms\_key\_sse) | Controls if S3 bucket policy should deny usage of incorrect KMS key SSE. | `bool` | `false` | no |
| <a name="input_attach_deny_unencrypted_object_uploads"></a> [attach\_deny\_unencrypted\_object\_uploads](#input\_attach\_deny\_unencrypted\_object\_uploads) | Controls if S3 bucket should deny unencrypted object uploads policy attached. | `bool` | `false` | no |
| <a name="input_attach_elb_log_delivery_policy"></a> [attach\_elb\_log\_delivery\_policy](#input\_attach\_elb\_log\_delivery\_policy) | Controls if S3 bucket should have ELB log delivery policy attached | `bool` | `false` | no |
| <a name="input_attach_inventory_destination_policy"></a> [attach\_inventory\_destination\_policy](#input\_attach\_inventory\_destination\_policy) | Controls if S3 bucket should have bucket inventory destination policy attached. | `bool` | `false` | no |
| <a name="input_attach_lb_log_delivery_policy"></a> [attach\_lb\_log\_delivery\_policy](#input\_attach\_lb\_log\_delivery\_policy) | Controls if S3 bucket should have ALB/NLB log delivery policy attached | `bool` | `false` | no |
| <a name="input_attach_policy"></a> [attach\_policy](#input\_attach\_policy) | Controls if S3 bucket should have bucket policy attached (set to `true` to use value of `policy` as bucket policy) | `bool` | `false` | no |
| <a name="input_attach_public_policy"></a> [attach\_public\_policy](#input\_attach\_public\_policy) | Controls if a user defined public bucket policy will be attached (set to `false` to allow upstream to apply defaults to the bucket) | `bool` | `true` | no |
| <a name="input_bucket"></a> [bucket](#input\_bucket) | (Optional, Forces new resource) The name of the bucket. If omitted, Terraform will assign a random, unique name. | `string` | `null` | no |
| <a name="input_bucket_key_enabled"></a> [bucket\_key\_enabled](#input\_bucket\_key\_enabled) | Whether or not to use [Amazon S3 Bucket Keys for SSE-KMS](https://docs.aws.amazon.com/AmazonS3/latest/userguide/bucket-key.html). | `bool` | `null` | no |
| <a name="input_bucket_prefix"></a> [bucket\_prefix](#input\_bucket\_prefix) | (Optional, Forces new resource) Creates a unique bucket name beginning with the specified prefix. Conflicts with bucket. | `string` | `null` | no |
| <a name="input_control_object_ownership"></a> [control\_object\_ownership](#input\_control\_object\_ownership) | Whether to manage S3 Bucket Ownership Controls on this bucket. | `bool` | `false` | no |
| <a name="input_cors_rule"></a> [cors\_rule](#input\_cors\_rule) | List of maps containing rules for Cross-Origin Resource Sharing. | `any` | `[]` | no |
| <a name="input_create_bucket"></a> [create\_bucket](#input\_create\_bucket) | Controls if S3 bucket should be created | `bool` | `true` | no |
| <a name="input_default_tags"></a> [default\_tags](#input\_default\_tags) | A map of default tags to be applied on each resource. You can get required tags [here](https://cloud.merck.com/documentation/compliance/tagging-standards/index.html) | `map(string)` | n/a | yes |
| <a name="input_expected_bucket_owner"></a> [expected\_bucket\_owner](#input\_expected\_bucket\_owner) | The account ID of the expected bucket owner | `string` | `null` | no |
| <a name="input_force_destroy"></a> [force\_destroy](#input\_force\_destroy) | (Optional, Default:false ) A boolean that indicates all objects should be deleted from the bucket so that the bucket can be destroyed without error. These objects are not recoverable. | `bool` | `false` | no |
| <a name="input_grant"></a> [grant](#input\_grant) | An ACL policy grant. Conflicts with `acl` | `any` | `[]` | no |
| <a name="input_intelligent_tiering"></a> [intelligent\_tiering](#input\_intelligent\_tiering) | Map containing intelligent tiering configuration. | `any` | `{}` | no |
| <a name="input_inventory_configuration"></a> [inventory\_configuration](#input\_inventory\_configuration) | Map containing S3 inventory configuration. | `any` | `{}` | no |
| <a name="input_inventory_self_source_destination"></a> [inventory\_self\_source\_destination](#input\_inventory\_self\_source\_destination) | Whether or not the inventory source bucket is also the destination bucket. | `bool` | `false` | no |
| <a name="input_inventory_source_account_id"></a> [inventory\_source\_account\_id](#input\_inventory\_source\_account\_id) | The inventory source account id. | `string` | `null` | no |
| <a name="input_inventory_source_bucket_arn"></a> [inventory\_source\_bucket\_arn](#input\_inventory\_source\_bucket\_arn) | The inventory source bucket ARN. | `string` | `null` | no |
| <a name="input_kms_key_id"></a> [kms\_key\_id](#input\_kms\_key\_id) | KMS Key for Replication | `string` | `null` | no |
| <a name="input_lifecycle_rule"></a> [lifecycle\_rule](#input\_lifecycle\_rule) | List of maps containing configuration of object lifecycle management. | `any` | `[]` | no |
| <a name="input_logging"></a> [logging](#input\_logging) | Map containing access bucket logging configuration.<br/><br/>Conditions for the logging configuration:<br/>  - If logging must be disabled, then create should be set to false, and target\_bucket should be null.<br/>  - If we want to use a log bucket that already exists, then create should be set to false, and target\_bucket should be set to "existing\_bucket\_name".<br/>  - If logging must be enabled, then create should be set to true<pre>type = object({<br/>  create        = bool<br/>  target_bucket = string<br/>  target_prefix = string<br/>  target_object_key_format = optional(object({<br/>    partitioned_prefix = optional(object({<br/>      partition_date_source = optional(map(string))   # Specifies the partition date source for the partitioned prefix. Valid values: EventTime, DeliveryTime.<br/>      simple_prefix = optional(bool)                   <br/>    }))<br/>})</pre> | <pre>object({<br/>    create        = optional(bool, true)<br/>    target_bucket = optional(string)<br/>    target_prefix = optional(string, "log/")<br/>    target_object_key_format = optional(object({<br/>      partitioned_prefix = optional(map(string))<br/>      simple_prefix      = optional(bool)<br/>    }))<br/>  })</pre> | <pre>{<br/>  "create": true,<br/>  "target_object_key_format": {<br/>    "simple_prefix": true<br/>  },<br/>  "target_prefix": "log/"<br/>}</pre> | no |
| <a name="input_metric_configuration"></a> [metric\_configuration](#input\_metric\_configuration) | Map containing bucket metric configuration. | `any` | `[]` | no |
| <a name="input_object_lock_configuration"></a> [object\_lock\_configuration](#input\_object\_lock\_configuration) | Map containing S3 object locking configuration. | `any` | `{}` | no |
| <a name="input_object_lock_enabled"></a> [object\_lock\_enabled](#input\_object\_lock\_enabled) | Whether S3 bucket should have an Object Lock configuration enabled. | `bool` | `false` | no |
| <a name="input_owner"></a> [owner](#input\_owner) | Bucket owner's display name and ID. Conflicts with `acl` | `map(string)` | `{}` | no |
| <a name="input_policy"></a> [policy](#input\_policy) | (Optional) A valid bucket policy JSON document. Note that if the policy document is not specific enough (but still valid), Terraform may view the policy as constantly changing in a terraform plan. In this case, please make sure you use the verbose/specific version of the policy. For more information about building AWS IAM policy documents with Terraform, see the AWS IAM Policy Document Guide. | `string` | `null` | no |
| <a name="input_replication_configuration"></a> [replication\_configuration](#input\_replication\_configuration) | Map containing cross-region replication configuration. | `any` | `{}` | no |
| <a name="input_request_payer"></a> [request\_payer](#input\_request\_payer) | (Optional) Specifies who should bear the cost of Amazon S3 data transfer. Can be either BucketOwner or Requester. By default, the owner of the S3 bucket would incur the costs of any data transfer. See Requester Pays Buckets developer guide for more information. | `string` | `null` | no |
| <a name="input_s3_log_acceleration_status"></a> [s3\_log\_acceleration\_status](#input\_s3\_log\_acceleration\_status) | (Optional) Sets the accelerate configuration of an existing bucket. Can be Enabled or Suspended. | `string` | `null` | no |
| <a name="input_s3_log_access_log_delivery_policy_source_accounts"></a> [s3\_log\_access\_log\_delivery\_policy\_source\_accounts](#input\_s3\_log\_access\_log\_delivery\_policy\_source\_accounts) | (Optional) List of AWS Account IDs should be allowed to deliver access logs to this bucket. | `list(string)` | `[]` | no |
| <a name="input_s3_log_access_log_delivery_policy_source_buckets"></a> [s3\_log\_access\_log\_delivery\_policy\_source\_buckets](#input\_s3\_log\_access\_log\_delivery\_policy\_source\_buckets) | (Optional) List of S3 bucket ARNs which should be allowed to deliver access logs to this bucket. | `list(string)` | `[]` | no |
| <a name="input_s3_log_attach_access_log_delivery_policy"></a> [s3\_log\_attach\_access\_log\_delivery\_policy](#input\_s3\_log\_attach\_access\_log\_delivery\_policy) | Controls if S3 bucket should have S3 access log delivery policy attached | `bool` | `true` | no |
| <a name="input_s3_log_attach_elb_log_delivery_policy"></a> [s3\_log\_attach\_elb\_log\_delivery\_policy](#input\_s3\_log\_attach\_elb\_log\_delivery\_policy) | Controls if S3 bucket should have ELB log delivery policy attached | `bool` | `false` | no |
| <a name="input_s3_log_attach_inventory_destination_policy"></a> [s3\_log\_attach\_inventory\_destination\_policy](#input\_s3\_log\_attach\_inventory\_destination\_policy) | Controls if S3 bucket should have bucket inventory destination policy attached. | `bool` | `false` | no |
| <a name="input_s3_log_attach_lb_log_delivery_policy"></a> [s3\_log\_attach\_lb\_log\_delivery\_policy](#input\_s3\_log\_attach\_lb\_log\_delivery\_policy) | Controls if S3 bucket should have ALB/NLB log delivery policy attached | `bool` | `false` | no |
| <a name="input_s3_log_attach_policy"></a> [s3\_log\_attach\_policy](#input\_s3\_log\_attach\_policy) | Controls if S3 bucket should have bucket policy attached (set to `true` to use value of `policy` as bucket policy) | `bool` | `false` | no |
| <a name="input_s3_log_attach_public_policy"></a> [s3\_log\_attach\_public\_policy](#input\_s3\_log\_attach\_public\_policy) | Controls if a user defined public bucket policy will be attached (set to `false` to allow upstream to apply defaults to the bucket) | `bool` | `true` | no |
| <a name="input_s3_log_bucket"></a> [s3\_log\_bucket](#input\_s3\_log\_bucket) | (Optional, Forces new resource) The name of the bucket. If omitted, Terraform will assign a random, unique name. | `string` | `null` | no |
| <a name="input_s3_log_bucket_prefix"></a> [s3\_log\_bucket\_prefix](#input\_s3\_log\_bucket\_prefix) | (Optional, Forces new resource) Creates a unique bucket name beginning with the specified prefix. Conflicts with bucket. | `string` | `null` | no |
| <a name="input_s3_log_control_object_ownership"></a> [s3\_log\_control\_object\_ownership](#input\_s3\_log\_control\_object\_ownership) | Whether to manage S3 Bucket Ownership Controls on this bucket. | `bool` | `false` | no |
| <a name="input_s3_log_cors_rule"></a> [s3\_log\_cors\_rule](#input\_s3\_log\_cors\_rule) | List of maps containing rules for Cross-Origin Resource Sharing. | `any` | `[]` | no |
| <a name="input_s3_log_expected_bucket_owner"></a> [s3\_log\_expected\_bucket\_owner](#input\_s3\_log\_expected\_bucket\_owner) | The account ID of the expected bucket owner | `string` | `null` | no |
| <a name="input_s3_log_force_destroy"></a> [s3\_log\_force\_destroy](#input\_s3\_log\_force\_destroy) | (Optional, Default:false ) A boolean that indicates all objects should be deleted from the bucket so that the bucket can be destroyed without error. These objects are not recoverable. | `bool` | `false` | no |
| <a name="input_s3_log_grant"></a> [s3\_log\_grant](#input\_s3\_log\_grant) | An ACL policy grant. Conflicts with `acl` | `any` | `[]` | no |
| <a name="input_s3_log_intelligent_tiering"></a> [s3\_log\_intelligent\_tiering](#input\_s3\_log\_intelligent\_tiering) | Map containing intelligent tiering configuration. | `any` | `{}` | no |
| <a name="input_s3_log_inventory_configuration"></a> [s3\_log\_inventory\_configuration](#input\_s3\_log\_inventory\_configuration) | Map containing S3 inventory configuration. | `any` | `{}` | no |
| <a name="input_s3_log_inventory_self_source_destination"></a> [s3\_log\_inventory\_self\_source\_destination](#input\_s3\_log\_inventory\_self\_source\_destination) | Whether or not the inventory source bucket is also the destination bucket. | `bool` | `false` | no |
| <a name="input_s3_log_inventory_source_account_id"></a> [s3\_log\_inventory\_source\_account\_id](#input\_s3\_log\_inventory\_source\_account\_id) | The inventory source account id. | `string` | `null` | no |
| <a name="input_s3_log_inventory_source_bucket_arn"></a> [s3\_log\_inventory\_source\_bucket\_arn](#input\_s3\_log\_inventory\_source\_bucket\_arn) | The inventory source bucket ARN. | `string` | `null` | no |
| <a name="input_s3_log_kms_key_id"></a> [s3\_log\_kms\_key\_id](#input\_s3\_log\_kms\_key\_id) | KMS Key for Replication | `string` | `null` | no |
| <a name="input_s3_log_lifecycle_rule"></a> [s3\_log\_lifecycle\_rule](#input\_s3\_log\_lifecycle\_rule) | List of maps containing configuration of object lifecycle management. | `any` | `[]` | no |
| <a name="input_s3_log_logging"></a> [s3\_log\_logging](#input\_s3\_log\_logging) | Map containing access bucket logging configuration. | `map(string)` | `{}` | no |
| <a name="input_s3_log_metric_configuration"></a> [s3\_log\_metric\_configuration](#input\_s3\_log\_metric\_configuration) | Map containing bucket metric configuration. | `any` | `[]` | no |
| <a name="input_s3_log_object_lock_configuration"></a> [s3\_log\_object\_lock\_configuration](#input\_s3\_log\_object\_lock\_configuration) | Map containing S3 object locking configuration. | `any` | `{}` | no |
| <a name="input_s3_log_object_lock_enabled"></a> [s3\_log\_object\_lock\_enabled](#input\_s3\_log\_object\_lock\_enabled) | Whether S3 bucket should have an Object Lock configuration enabled. | `bool` | `false` | no |
| <a name="input_s3_log_owner"></a> [s3\_log\_owner](#input\_s3\_log\_owner) | Bucket owner's display name and ID. Conflicts with `acl` | `map(string)` | `{}` | no |
| <a name="input_s3_log_policy"></a> [s3\_log\_policy](#input\_s3\_log\_policy) | (Optional) A valid bucket policy JSON document. Note that if the policy document is not specific enough (but still valid), Terraform may view the policy as constantly changing in a terraform plan. In this case, please make sure you use the verbose/specific version of the policy. For more information about building AWS IAM policy documents with Terraform, see the AWS IAM Policy Document Guide. | `string` | `null` | no |
| <a name="input_s3_log_replication_configuration"></a> [s3\_log\_replication\_configuration](#input\_s3\_log\_replication\_configuration) | Map containing cross-region replication configuration. | `any` | `{}` | no |
| <a name="input_s3_log_request_payer"></a> [s3\_log\_request\_payer](#input\_s3\_log\_request\_payer) | (Optional) Specifies who should bear the cost of Amazon S3 data transfer. Can be either BucketOwner or Requester. By default, the owner of the S3 bucket would incur the costs of any data transfer. See Requester Pays Buckets developer guide for more information. | `string` | `null` | no |
| <a name="input_s3_log_sse_algorithm"></a> [s3\_log\_sse\_algorithm](#input\_s3\_log\_sse\_algorithm) | KMS Key for Replication | `string` | `"aws:kms"` | no |
| <a name="input_s3_log_tags"></a> [s3\_log\_tags](#input\_s3\_log\_tags) | (Optional) A mapping of tags to assign to the bucket. | `map(string)` | `{}` | no |
| <a name="input_s3_log_transition_default_minimum_object_size"></a> [s3\_log\_transition\_default\_minimum\_object\_size](#input\_s3\_log\_transition\_default\_minimum\_object\_size) | The default minimum object size behavior applied to the lifecycle configuration. Valid values: all\_storage\_classes\_128K (default), varies\_by\_storage\_class | `string` | `null` | no |
| <a name="input_s3_log_versioning"></a> [s3\_log\_versioning](#input\_s3\_log\_versioning) | Map containing versioning configuration. | `map(string)` | <pre>{<br/>  "mfa_delete": "disabled",<br/>  "status": "enabled"<br/>}</pre> | no |
| <a name="input_s3_log_website"></a> [s3\_log\_website](#input\_s3\_log\_website) | Map containing static web-site hosting or redirect configuration. | `any` | `{}` | no |
| <a name="input_sse_algorithm"></a> [sse\_algorithm](#input\_sse\_algorithm) | KMS Key for Replication | `string` | `"aws:kms"` | no |
| <a name="input_tags"></a> [tags](#input\_tags) | (Optional) A mapping of tags to assign to the bucket. | `map(string)` | `{}` | no |
| <a name="input_transition_default_minimum_object_size"></a> [transition\_default\_minimum\_object\_size](#input\_transition\_default\_minimum\_object\_size) | The default minimum object size behavior applied to the lifecycle configuration. Valid values: all\_storage\_classes\_128K (default), varies\_by\_storage\_class | `string` | `null` | no |
| <a name="input_versioning"></a> [versioning](#input\_versioning) | Map containing versioning configuration. | `map(string)` | <pre>{<br/>  "mfa_delete": "disabled",<br/>  "status": "enabled"<br/>}</pre> | no |
| <a name="input_website"></a> [website](#input\_website) | Map containing static web-site hosting or redirect configuration. | `any` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_s3_bucket_arn"></a> [s3\_bucket\_arn](#output\_s3\_bucket\_arn) | The ARN of the bucket. Will be of format arn:aws:s3:::bucketname. |
| <a name="output_s3_bucket_bucket_domain_name"></a> [s3\_bucket\_bucket\_domain\_name](#output\_s3\_bucket\_bucket\_domain\_name) | The bucket domain name. Will be of format bucketname.s3.amazonaws.com. |
| <a name="output_s3_bucket_bucket_regional_domain_name"></a> [s3\_bucket\_bucket\_regional\_domain\_name](#output\_s3\_bucket\_bucket\_regional\_domain\_name) | The bucket region-specific domain name. The bucket domain name including the region name, please refer here for format. Note: The AWS CloudFront allows specifying S3 region-specific endpoint when creating S3 origin, it will prevent redirect issues from CloudFront to S3 Origin URL. |
| <a name="output_s3_bucket_hosted_zone_id"></a> [s3\_bucket\_hosted\_zone\_id](#output\_s3\_bucket\_hosted\_zone\_id) | The Route 53 Hosted Zone ID for this bucket's region. |
| <a name="output_s3_bucket_id"></a> [s3\_bucket\_id](#output\_s3\_bucket\_id) | The name of the bucket. |
| <a name="output_s3_bucket_lifecycle_configuration_rules"></a> [s3\_bucket\_lifecycle\_configuration\_rules](#output\_s3\_bucket\_lifecycle\_configuration\_rules) | The lifecycle rules of the bucket, if the bucket is configured with lifecycle rules. If not, this will be an empty string. |
| <a name="output_s3_bucket_policy"></a> [s3\_bucket\_policy](#output\_s3\_bucket\_policy) | The policy of the bucket, if the bucket is configured with a policy. If not, this will be an empty string. |
| <a name="output_s3_bucket_region"></a> [s3\_bucket\_region](#output\_s3\_bucket\_region) | The AWS region this bucket resides in. |
| <a name="output_s3_bucket_website_domain"></a> [s3\_bucket\_website\_domain](#output\_s3\_bucket\_website\_domain) | The domain of the website endpoint, if the bucket is configured with a website. If not, this will be an empty string. This is used to create Route 53 alias records. |
| <a name="output_s3_bucket_website_endpoint"></a> [s3\_bucket\_website\_endpoint](#output\_s3\_bucket\_website\_endpoint) | The website endpoint, if the bucket is configured with a website. If not, this will be an empty string. |
| <a name="output_s3_log_bucket_arn"></a> [s3\_log\_bucket\_arn](#output\_s3\_log\_bucket\_arn) | The ARN of the bucket. Will be of format arn:aws:s3:::bucketname. |
| <a name="output_s3_log_bucket_bucket_domain_name"></a> [s3\_log\_bucket\_bucket\_domain\_name](#output\_s3\_log\_bucket\_bucket\_domain\_name) | The bucket domain name. Will be of format bucketname.s3.amazonaws.com. |
| <a name="output_s3_log_bucket_bucket_regional_domain_name"></a> [s3\_log\_bucket\_bucket\_regional\_domain\_name](#output\_s3\_log\_bucket\_bucket\_regional\_domain\_name) | The bucket region-specific domain name. The bucket domain name including the region name, please refer here for format. Note: The AWS CloudFront allows specifying S3 region-specific endpoint when creating S3 origin, it will prevent redirect issues from CloudFront to S3 Origin URL. |
| <a name="output_s3_log_bucket_hosted_zone_id"></a> [s3\_log\_bucket\_hosted\_zone\_id](#output\_s3\_log\_bucket\_hosted\_zone\_id) | The Route 53 Hosted Zone ID for this bucket's region. |
| <a name="output_s3_log_bucket_id"></a> [s3\_log\_bucket\_id](#output\_s3\_log\_bucket\_id) | The name of the bucket. |
| <a name="output_s3_log_bucket_lifecycle_configuration_rules"></a> [s3\_log\_bucket\_lifecycle\_configuration\_rules](#output\_s3\_log\_bucket\_lifecycle\_configuration\_rules) | The lifecycle rules of the bucket, if the bucket is configured with lifecycle rules. If not, this will be an empty string. |
| <a name="output_s3_log_bucket_policy"></a> [s3\_log\_bucket\_policy](#output\_s3\_log\_bucket\_policy) | The policy of the bucket, if the bucket is configured with a policy. If not, this will be an empty string. |
| <a name="output_s3_log_bucket_region"></a> [s3\_log\_bucket\_region](#output\_s3\_log\_bucket\_region) | The AWS region this bucket resides in. |
| <a name="output_s3_log_bucket_website_domain"></a> [s3\_log\_bucket\_website\_domain](#output\_s3\_log\_bucket\_website\_domain) | The domain of the website endpoint, if the bucket is configured with a website. If not, this will be an empty string. This is used to create Route 53 alias records. |
| <a name="output_s3_log_bucket_website_endpoint"></a> [s3\_log\_bucket\_website\_endpoint](#output\_s3\_log\_bucket\_website\_endpoint) | The website endpoint, if the bucket is configured with a website. If not, this will be an empty string. |
<!-- END_TF_DOCS -->
