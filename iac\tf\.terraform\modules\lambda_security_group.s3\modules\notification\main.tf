moved {
  from = module.s3_notification_wrapper
  to   = module.notification
}

#>>GENERATE_CONTENT
module "notification" {
  source  = "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/s3-bucket/aws//modules/notification"
  version = "4.2.2"

  bucket               = var.bucket
  bucket_arn           = var.bucket_arn
  create               = var.create
  create_sns_policy    = var.create_sns_policy
  create_sqs_policy    = var.create_sqs_policy
  eventbridge          = var.eventbridge
  lambda_notifications = var.lambda_notifications
  sns_notifications    = var.sns_notifications
  sqs_notifications    = var.sqs_notifications
}

#>>GENERATE_CONTENT