############
# common #
##############
resource_vars = {
  appname     = "icauto"
  region      = "cn-north-1"
  attributes  = ["iac"]
  environment = "uat"
  label_order = ["appname", "environment"]
}

default_tags = {
  Application        = "icauto"
  Consumer           = "<EMAIL>"
  Environment        = "uat"
  DataClassification = "Proprietary"
  Service            = "Infra Automation resource deployment-Test"
  #  Costcenter         = "********"
  Created   = "2025-05-16" #"Resource created date"
  CreatedBy = "CloudSE_IAC"
}

tags = {
  "DoNotDelete" = "True"
}
# You need to get these from our aws account!
account_no     = "************"
region         = "cn-north-1"
vpc_id         = "vpc-0f666efa794251c3e"
vpc_subnet_ids = ["subnet-0dc57cf167a4181e5", "subnet-0eef172944d35f94a"]

# You need to create your own deployment role. Use iam policy in the iam folder of this example.
deployment_role = "deployment-role"
partition       = "aws-cn"

