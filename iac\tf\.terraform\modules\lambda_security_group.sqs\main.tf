#>>GENERATE_CONTENT
module "sqs_wrapper" {
  source  = "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/sqs/aws"
  version = "4.2.1"

  content_based_deduplication           = var.content_based_deduplication
  create                                = var.create
  create_dlq                            = var.create_dlq
  create_dlq_queue_policy               = var.create_dlq_queue_policy
  create_dlq_redrive_allow_policy       = var.create_dlq_redrive_allow_policy
  create_queue_policy                   = true
  deduplication_scope                   = var.deduplication_scope
  delay_seconds                         = var.delay_seconds
  dlq_content_based_deduplication       = var.dlq_content_based_deduplication
  dlq_deduplication_scope               = var.dlq_deduplication_scope
  dlq_delay_seconds                     = var.dlq_delay_seconds
  dlq_kms_data_key_reuse_period_seconds = var.dlq_kms_data_key_reuse_period_seconds
  dlq_kms_master_key_id                 = var.dlq_kms_master_key_id
  dlq_message_retention_seconds         = var.dlq_message_retention_seconds
  dlq_name                              = var.dlq_name
  dlq_queue_policy_statements           = var.dlq_queue_policy_statements
  dlq_receive_wait_time_seconds         = var.dlq_receive_wait_time_seconds
  dlq_redrive_allow_policy              = var.dlq_redrive_allow_policy
  dlq_sqs_managed_sse_enabled           = var.dlq_sqs_managed_sse_enabled
  dlq_tags                              = var.dlq_tags
  dlq_visibility_timeout_seconds        = var.dlq_visibility_timeout_seconds
  fifo_queue                            = var.fifo_queue
  fifo_throughput_limit                 = var.fifo_throughput_limit
  kms_data_key_reuse_period_seconds     = var.kms_data_key_reuse_period_seconds
  kms_master_key_id                     = var.kms_master_key_id
  max_message_size                      = var.max_message_size
  message_retention_seconds             = var.message_retention_seconds
  name                                  = var.name
  override_dlq_queue_policy_documents   = var.override_dlq_queue_policy_documents
  override_queue_policy_documents       = var.override_queue_policy_documents
  queue_policy_statements               = var.queue_policy_statements
  receive_wait_time_seconds             = var.receive_wait_time_seconds
  redrive_allow_policy                  = var.redrive_allow_policy
  redrive_policy                        = var.redrive_policy
  source_dlq_queue_policy_documents     = var.source_dlq_queue_policy_documents
  source_queue_policy_documents         = var.source_queue_policy_documents
  sqs_managed_sse_enabled               = true
  tags                                  = module.tags.tags
  use_name_prefix                       = var.use_name_prefix
  visibility_timeout_seconds            = var.visibility_timeout_seconds
}

#>>GENERATE_CONTENT