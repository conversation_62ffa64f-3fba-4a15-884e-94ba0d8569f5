# Variables
variable "resource_vars" {
  description = "map of tags to pass to labels module"
  type = object({
    appname     = string
    region      = string
    environment = string
    attributes  = list(string)
    label_order = list(string)
  })
}

variable "default_tags" {
  description = "A map of default tags to be applied on each resource. You can get required tags [here](https://cloud.merck.com/documentation/compliance/tagging-standards/index.html)"
  type        = map(string)
}

variable "region" {
  description = "AWS Region"
  type        = string
  default     = "us-east-1"
}

variable "deployment_role" {
  description = "Terraform Deployment Role"
  type        = string
}

variable "account_no" {
  description = "AWS account number to deploy to"
  type        = string
}

variable "subscriptions" {
  description = "A map of subscription definitions to create"
  type        = any
  default     = {}
}

variable "other_subscriptions" {
  description = "A map of subscription definitions to create"
  type        = any
  default     = {}
}

variable "partition" {
  type        = string
  default     = "aws"
  description = "Second part of aws arn. Used only in the provider configuration."
}
