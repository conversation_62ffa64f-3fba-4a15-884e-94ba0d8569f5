#! /bin/bash

: """${MERCK_GITHUB_ORG=merck-gen}"""
: """${BRANCH=main}"""

set_source(){
    VERSION=${1}
    REPO_NAME=${2}
    IS_PATTERN=${3}

    cd ${WORKSPACE}
    git remote set-url origin "https://${SRVUSER}:${TOKEN}@github.com/merck-gen/${REPO_NAME}"

    git checkout main

    REPLACE_STR="https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/${REPO_NAME}_${VERSION}.tgz"
    if $IS_PATTERN; then
        REPLACE_STR="https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/patterns/${REPO_NAME}_${VERSION}.tgz"
    fi

    for example in examples/*; do
        if [[ -f "$example/main.tf" ]]; then
            sed -i "s|\"\.\./\.\.[/]*\"|\"$REPLACE_STR\"|g" $example/main.tf
            sed -i "s|\"\.\./\.\.|\"$REPLACE_STR/|g" $example/main.tf
        fi
    done

    git add examples/
    git commit -s -m "Release commit for $VERSION. [ci skip]"
    git push
}

tag_repo(){
    VERSION_TAG=${1}
    REPO_NAME=${2}
    git remote set-url origin "https://${SRVUSER}:${TOKEN}@github.com/merck-gen/${REPO_NAME}"
    # Tag the cloned repo
    cd ${WORKSPACE}
    git tag ${VERSION_TAG}
    # Push tag to github repo
    echo $PWD
    git push -f --tags
}

create_release() {
    VERSION_TAG=${1}
    REPO_NAME=${2}

    curl -L \
        -X POST \
        -H "Accept: application/vnd.github+json" \
        -H "Authorization: Bearer ${TOKEN}"\
        -H "X-GitHub-Api-Version: 2022-11-28" \
        https://api.github.com/repos/${MERCK_GITHUB_ORG}/${REPO_NAME}/releases \
        -d "{\"tag_name\":\"${VERSION_TAG}\",\"target_commitish\":\"${BRANCH}\",\"name\":\"${VERSION_TAG}\",\"body\":\"Version: ${VERSION_TAG} has been released for ${REPO_NAME}.\",\"draft\":false,\"prerelease\":false,\"generate_release_notes\":false}"
}

set_next_version(){
    NEXT_VERSION=${1}
    REPO_NAME=${2}
    git remote set-url origin "https://${SRVUSER}:${TOKEN}@github.com/merck-gen/${REPO_NAME}"

    git checkout main
    echo INFO: Next version is ${NEXT_VERSION}
    git add ${WORKSPACE}/VERSION
    git commit --allow-empty -am "Set next main version to: ${NEXT_VERSION} [ci skip]"
    git push -f origin main:main
}


revert_release_commit(){
    VERSION=${1}
    REPO_NAME=${2}

    cd ${WORKSPACE}

    git remote set-url origin "https://${SRVUSER}:${TOKEN}@github.com/merck-gen/${REPO_NAME}"
    COMMIT=($(git log -10 --format="%H %s" | grep "Release commit for $VERSION."))
    echo "Retrieved commit to be reverted ${COMMIT[@]}"
    # skip in case there was no commit done eg. no examples
    if [[ ${#COMMIT[*]} -gt 1 ]]; then
        git revert --no-edit "${COMMIT[0]}"
        git push
    else 
        echo "git log: "
        echo $(git log -10 --format="%H %s" )
    fi
}

# Main
ACTION=${1}
VERSION=${2}

REPO_NAME=$(basename -s .git `git config --get remote.origin.url`)

IS_PATTERN=false 
if [[ $REPO_NAME == *"pattern"* ]]; then 
    IS_PATTERN=true
fi

if [ "$ACTION" == "tag_repo" ]; then
    set_source ${VERSION} ${REPO_NAME} ${IS_PATTERN}
    tag_repo ${VERSION} ${REPO_NAME}
    create_release ${VERSION} ${REPO_NAME}
elif [ "$ACTION" == "set_next_version" ]; then
    set_next_version ${VERSION} ${REPO_NAME}
elif [ "$ACTION" == "revert_release_commit" ]; then
    revert_release_commit ${VERSION} ${REPO_NAME}
fi