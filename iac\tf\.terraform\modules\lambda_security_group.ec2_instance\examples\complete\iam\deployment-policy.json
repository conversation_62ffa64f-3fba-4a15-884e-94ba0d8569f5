{"Statement": [{"Action": ["dynamodb:DeleteItem", "dynamodb:GetItem", "dynamodb:PutItem", "ec2:AuthorizeSecurityGroupEgress", "ec2:AuthorizeSecurityGroupIngress", "ec2:CreateSecurityGroup", "ec2:CreateTags", "ec2:DeleteSecurityGroup", "ec2:DescribeImages", "ec2:DescribeInstanceAttribute", "ec2:DescribeInstanceCreditSpecifications", "ec2:DescribeInstanceTypes", "ec2:DescribeInstances", "ec2:DescribeNetworkInterfaces", "ec2:DescribeSecurityGroupRules", "ec2:DescribeSecurityGroups", "ec2:DescribeTags", "ec2:DescribeVolumes", "ec2:DescribeVpcs", "ec2:ModifyInstanceAttribute", "ec2:MonitorInstances", "ec2:RevokeSecurityGroupEgress", "ec2:RevokeSecurityGroupIngress", "ec2:RunInstances", "ec2:TerminateInstances", "iam:AddRoleToInstanceProfile", "iam:AttachRolePolicy", "iam:CreateInstanceProfile", "iam:CreatePolicy", "iam:CreateRole", "iam:DeleteInstanceProfile", "iam:DeletePolicy", "iam:DeleteRole", "iam:DetachRolePolicy", "iam:GetInstanceProfile", "iam:GetPolicy", "iam:GetPolicyVersion", "iam:GetRole", "iam:ListAttachedRolePolicies", "iam:ListInstanceProfilesForRole", "iam:ListPolicyVersions", "iam:ListRolePolicies", "iam:PassRole", "iam:RemoveRoleFromInstanceProfile", "iam:TagInstanceProfile", "iam:TagPolicy", "iam:TagRole", "iam:UntagPolicy", "iam:UntagRole", "kms:<PERSON><PERSON><PERSON><PERSON><PERSON>", "kms:CreateGrant", "kms:C<PERSON><PERSON><PERSON>", "kms:DeleteAlias", "kms:DescribeKey", "kms:EnableKeyRotation", "kms:GenerateDataKeyWithoutPlaintext", "kms:GetKeyPolicy", "kms:GetKeyRotationStatus", "kms:ListAliases", "kms:ListResourceTags", "kms:PutKeyPolicy", "kms:ScheduleKeyDeletion", "kms:TagResource", "kms:UntagResource", "logs:CreateLogGroup", "logs:DeleteLogGroup", "logs:DescribeLogGroups", "logs:ListTagsForResource", "logs:ListTagsLogGroup", "logs:PutRetentionPolicy", "logs:TagResource", "logs:UntagResource", "s3:GetObject", "s3:ListBucket", "s3:PutObject", "sts:<PERSON><PERSON>Role", "sts:GetCallerIdentity"], "Effect": "Allow", "Resource": "*"}], "Version": "2012-10-17"}