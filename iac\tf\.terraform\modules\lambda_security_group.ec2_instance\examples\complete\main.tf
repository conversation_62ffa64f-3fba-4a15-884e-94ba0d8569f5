data "aws_region" "current" {}
locals {
  deployment_role_arn = "arn:${data.aws_partition.current.partition}:iam::${var.account_no}:role/${var.deployment_role}"
  ec2_role_name       = module.labels.id
  failed_value        = "failed"
  result_tag          = "initialization"
  user_data = templatefile("user-data.sh.tpl", {
    failed_value  = local.failed_value
    success_value = "success"
    result_tag    = local.result_tag
    account_no    = var.account_no
    # Has to have "ec2:CreateTags permission!"
    role_name = local.ec2_role_name
    region    = var.region
    partition = data.aws_partition.current.partition
  })
}

data "aws_ami" "msd_al2" {
  count       = var.enable_ami_lookup ? 1 : 0
  most_recent = true
  name_regex  = "^AL2023_ImageFactory.*"

  filter {
    name   = "root-device-type"
    values = ["ebs"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}

data "aws_partition" "current" {}

###############################
# Labels Module               #
###############################

module "labels" {
  source  = "artifacts.merck.com/terraform-iac-shared__internal/labels/aws"
  version = "~> 4.0"
  enabled = true

  appname     = var.resource_vars.appname
  region      = var.resource_vars.region
  attributes  = var.resource_vars.attributes
  label_order = var.resource_vars.label_order
  environment = var.resource_vars.environment
}

###############################
# EC2 Module                  #
###############################

module "ec2_instance" {
  # Update this source to use the Artifactory URL of a released version of the module: https://go.merck.com/iacreleases
  source  = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/ec2-instance/aws"
  version = "2.4.0"
  #enable_ebs_creation          = false
  name                          = "${module.labels.id}-ec2-testing"
  ami                           = var.ami != null ? var.ami : data.aws_ami.msd_al2[0].id
  instance_type                 = var.instance_type
  availability_zone             = var.availability_zone
  subnet_id                     = var.subnet_id
  vpc_security_group_ids        = [module.ec2_security_group.security_group_id]
  key_name                      = var.key_pair_name
  iam_instance_profile          = var.iam_instance_profile
  user_data_replace_on_change   = var.user_data_replace_on_change
  enable_volume_tags            = var.enable_volume_tags
  root_block_device_volume_size = var.root_block_device_volume_size
  user_data                     = local.user_data
  root_block_tags = {
    Name = "${module.labels.id}-root-block"
  }
  root_block_device_kms = module.aws_kms_key.key_arn
  ebs_volumes = [{
    device_name = "/dev/sdf"
    volume_size = 20
    volume_type = "gp3"
    kms_key_id  = module.aws_kms_key.key_arn
    throughput  = 125
    },
    {
      device_name = "/dev/sdh"
      volume_size = 20
      volume_type = "gp3"
      kms_key_id  = module.aws_kms_key.key_arn
      throughput  = 125
    }
  ]
  tags                     = var.tags
  default_tags             = var.default_tags
  iam_role_name            = local.ec2_role_name
  iam_role_use_name_prefix = false
  iam_role_description     = "IAM role for EC2 instance"

  iam_role_tags = var.default_tags
  iam_role_policies = {
    AddTags                      = module.create_tags_policy.arn
    AmazonSSMManagedInstanceCore = "arn:${data.aws_partition.current.partition}:iam::aws:policy/AmazonSSMManagedInstanceCore"
    AmazonSSMPatchAssociation    = "arn:${data.aws_partition.current.partition}:iam::aws:policy/AmazonSSMPatchAssociation"
    AdministratorAccess          = "arn:${data.aws_partition.current.partition}:iam::aws:policy/AmazonS3ReadOnlyAccess"
  }
  wait_for_instance = {
    enabled             = true # Setting the value to false for terratest checks in GHA
    deployment_role_arn = local.deployment_role_arn
    tag_name            = local.result_tag   # has to match the tagging done in the userdata script!
    failed_value        = local.failed_value # has to match the tagging done in the userdata script!
  }
}

################################
# Supporting Resources         #
################################

module "create_tags_policy" {
  source        = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/iam/aws//modules/iam-policy"
  version       = "~> 2.2"
  create_policy = true
  name          = module.labels.id
  policy = jsonencode(
    {
      "Version" : "2012-10-17"
      "Statement" : [
        {
          "Effect" : "Allow",
          "Action" : [
            "ec2:CreateTags"
          ],
          "Resource" : "*",
        }
      ]
    }
  )
  default_tags = var.default_tags
}

########## KMS Key ###########
module "aws_kms_key" {
  source       = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/kms/aws"
  version      = "~> 2.4"
  description  = "Key for encrypting EBS volumes and Cloudwatch Log Group"
  key_usage    = "ENCRYPT_DECRYPT"
  policy       = data.aws_iam_policy_document.kms_default.json
  tags         = {}
  default_tags = var.default_tags
  aliases      = ["alias/${module.labels.id}-ec2_cloudwatch"]
  multi_region = var.kms_use_multi_region
}

########## Cloudwatch Log Group ###########
module "cloudwatch_log_group" {
  depends_on        = [module.aws_kms_key]
  source            = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/cloudwatch/aws//modules/log-group"
  version           = "~> 2.0"
  name              = module.labels.id
  retention_in_days = "7"
  kms_key_id        = module.aws_kms_key.key_arn
  tags              = var.tags
  default_tags      = var.default_tags
}

########## Security Group - EC2 ###########
module "ec2_security_group" {
  source                  = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/security-group/aws"
  version                 = "~> 2.5"
  name                    = "${module.labels.id}-ec2-sg"
  description             = "Security group for EC2 Instance example"
  vpc_id                  = var.vpc_id
  ingress_prefix_list_ids = var.prefix_list_ids
  ingress_with_cidr_blocks = [
    {
      from_port   = 2022
      to_port     = 2022
      protocol    = "tcp"
      description = "ssh"
      cidr_blocks = "***********/27,***********/27" # allow ssh access only for the aap https://share.merck.com/display/DSO/AAP+Low+Level+Architecture
    }
  ]
  egress_rules = ["all-all"]
  tags         = var.tags
  default_tags = var.default_tags
}
