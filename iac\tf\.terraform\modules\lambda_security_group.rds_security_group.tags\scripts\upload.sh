#!/bin/bash

# Artifactory upload script for Terraform modules.
#
# Example:
# SRVUSER=automation-user SRVPASS=*** ./upload.sh

set -euo pipefail

: """${VERSION=""}"""
: """${IS_RELEASE="false"}"""
: """${SUFFIX=$([[ ! -z "${VERSION}" ]] && echo "${VERSION}" || echo $(date +%m%d%Y)_$(echo ${GIT_COMMIT} | cut -c-7))}"""
: """${PACKAGE_NAME=$(basename $(git remote get-url origin) .git)_${SUFFIX}.tgz}"""
: """${ARTIFACTORY_URL="https://artifacts.merck.com/artifactory"}"""
: """${ARTIFACTORY_REPO="generic-iac-shared$([[ "${IS_RELEASE}" = "true" ]] && echo "" || echo "-dev")-local"}"""
: """${ARTIFACTORY_PATH="terraform/$([[ "${IS_RELEASE}" = "true" ]] && echo "releases" || echo "master")"}"""
: """${SKIP_GIT_STASH=false}"""
: """${SRVUSER=""}"""
: """${SRVPASS=""}"""

#Check if in the package name there is the string "pattern".
if [[ "${PACKAGE_NAME}" = *"pattern"* ]]; then
  if [[ "${IS_RELEASE}" = "true" ]]; then
     ARTIFACTORY_PATH="terraform/releases/patterns"
  else
     ARTIFACTORY_PATH="terraform/master/patterns"
  fi
fi

# Cleanup the workarea from files not tracked by Git
if [ "${SKIP_GIT_STASH}" = false ] && [ "${IS_RELEASE}" != "true" ]; then
    git stash --include-untracked
fi

git clean -fdx

# Pack the files
tar czf "${PACKAGE_NAME}.tgz" \
    --exclude="**/.external_modules" \
    --exclude="**/.git" \
    --exclude="**/.github" \
    --exclude="**/.terraform" \
    .

# Upload to Artifactory
jfrog rt upload \
    --url $ARTIFACTORY_URL \
    --user "${SRVUSER}" \
    --password "${SRVPASS}" \
    --build-name "${JOB_NAME}" \
    --build-number "${BUILD_NUMBER}" \
    "${PACKAGE_NAME}.tgz" \
    "${ARTIFACTORY_REPO}/${ARTIFACTORY_PATH}/${PACKAGE_NAME}"
# Publish build info for package
jfrog rt bp \
    --url "${ARTIFACTORY_URL}" \
    --user "${SRVUSER}" \
    --password "${SRVPASS}" \
    "${JOB_NAME}" \
    "${BUILD_NUMBER}"
echo "The module package can be found from:"
echo "${ARTIFACTORY_URL}/${ARTIFACTORY_REPO}/${ARTIFACTORY_PATH}/${PACKAGE_NAME}"
if [ "${SKIP_GIT_STASH}" = false ] && [ "${IS_RELEASE}" != "true"  ]; then
    git stash pop
fi
