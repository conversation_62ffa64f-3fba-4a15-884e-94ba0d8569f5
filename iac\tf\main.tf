###############################
# Labels Module
###############################
module "labels" {
  # Update this source to use the Artifactory URL of a released version of the module: https://go.merck.com/iacreleases
  source      = "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-labels-int_4.0.1.tgz"
  enabled     = true
  appname     = var.resource_vars.appname
  region      = var.resource_vars.region
  attributes  = var.resource_vars.attributes
  environment = var.resource_vars.environment
  label_order = var.resource_vars.label_order
}

locals {
  name                  = module.labels.id
  lambda_role_name      = "${local.name}-lambda-ecs-scheduler"
  eventbridge_role_name = "${local.name}-eventbridge-ecs-scheduler"

  # Define the ECS cluster ARN
  ecs_cluster_arn = "arn:aws-cn:ecs:cn-north-1:051953029949:cluster/msdpd-uatv-ecs-fargate"

  # Define the schedules for start and stop operations
  start_schedule = "cron(0 8 ? * MON-FRI *)"  # 8:00 AM Monday-Friday
  stop_schedule  = "cron(0 18 ? * MON-FRI *)" # 6:00 PM Monday-Friday

  # IAM policies for Lambda to manage ECS services
  lambda_policy_arns = [
    "arn:aws-cn:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy",
    "arn:aws-cn:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
    "arn:aws-cn:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole",
  ]

  # Lambda function source path
  lambda_source_path = "../../src/scheduler"
  lambda_output_path = "${path.module}/lambda_function.zip"
}

# Create a zip file of the Lambda function
data "archive_file" "lambda_scheduler_zip" {
  type        = "zip"
  source_dir  = local.lambda_source_path
  output_path = local.lambda_output_path
}

module "cron" {
  source            = "artifacts.merck.com/terraform-iac-shared__patterns/generic/aws"
  version           = "3.1.1"
  use_global_vpc_id = true
  global_vpc_id     = var.vpc_id

  lambda = {
    "${local.name}-ecs-scheduler" = {
      create_role            = true
      store_on_s3            = false
      create_package         = false
      publish                = true
      handler                = "main.lambda_handler"
      runtime                = "python3.12"
      timeout                = "300"
      vpc_subnet_ids         = var.vpc_subnet_ids
      local_existing_package = data.archive_file.lambda_scheduler_zip.output_path

      role_name = local.lambda_role_name

      # Environment variables
      environment_variables = {
        ECS_CLUSTER_ARN = local.ecs_cluster_arn
      }

      # Attach necessary policies
      attach_policies    = true
      number_of_policies = length(local.lambda_policy_arns)
      policies           = local.lambda_policy_arns

      # Additional policy statements for ECS service management
      attach_policy_statements = true
      policy_statements = {
        ecs_management = {
          effect    = "Allow",
          actions   = [
            "ecs:ListServices",
            "ecs:DescribeServices",
            "ecs:UpdateService"
          ],
          resources = ["*"]
        }
      }

      # Lambda trigger
      create_unqualified_alias_allowed_triggers = true
      create_current_version_allowed_triggers   = false
      allowed_triggers = {
        ExecutionFromEvents = {
          statement_id = "AllowExecutionFromEvents"
          action       = "lambda:InvokeFunction"
          service      = "events"
        }
      }

      tags = var.tags
    }
  }

  lambda_security_group = {
    "${local.name}-ecs-scheduler" = {
      description  = "Security group for ECS scheduler Lambda function"
      egress_rules = ["all-all"]
    }
  }

  eventbridge = {
    "${local.name}-ecs-scheduler" = {
      create_bus = false
      role_name  = local.eventbridge_role_name

      create_rules = true
      rules = {
        "${local.name}-start-ecs" = {
          description         = "Trigger Lambda to start ECS services"
          schedule_expression = local.start_schedule
        },
        "${local.name}-stop-ecs" = {
          description         = "Trigger Lambda to stop ECS services"
          schedule_expression = local.stop_schedule
        }
      }

      targets = {
        "${local.name}-start-ecs" = [
          {
            name  = "${local.name}-start-ecs"
            arn   = module.cron.lambda["${local.name}-ecs-scheduler"].lambda_function_arn
            input = jsonencode({
              action  = "start"
              cluster = local.ecs_cluster_arn
            })
          }
        ],
        "${local.name}-stop-ecs" = [
          {
            name  = "${local.name}-stop-ecs"
            arn   = module.cron.lambda["${local.name}-ecs-scheduler"].lambda_function_arn
            input = jsonencode({
              action  = "stop"
              cluster = local.ecs_cluster_arn
            })
          }
        ]
      }
    }
  }

  default_tags = var.default_tags
}
