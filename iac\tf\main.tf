###############################
# Labels Module
###############################
module "labels" {
  # Update this source to use the Artifactory URL of a released version of the module: https://go.merck.com/iacreleases
  source      = "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-labels-int_4.0.1.tgz"
  enabled     = true
  appname     = var.resource_vars.appname
  region      = var.resource_vars.region
  attributes  = var.resource_vars.attributes
  environment = var.resource_vars.environment
  label_order = var.resource_vars.label_order
}

module "cron" {
  source            = "artifacts.merck.com/terraform-iac-shared__patterns/generic/aws"
  version           = "3.1.1"
  use_global_vpc_id = true
  global_vpc_id     = var.vpc_id

  default_tags = var.default_tags
}


