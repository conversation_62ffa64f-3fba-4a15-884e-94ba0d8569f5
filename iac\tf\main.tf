###############################
# Labels Module
###############################
module "labels" {
  # Update this source to use the Artifactory URL of a released version of the module: https://go.merck.com/iacreleases
  source      = "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-labels-int_4.0.1.tgz"
  enabled     = true
  appname     = var.resource_vars.appname
  region      = var.resource_vars.region
  attributes  = var.resource_vars.attributes
  environment = var.resource_vars.environment
  label_order = var.resource_vars.label_order
}

locals {
  name                  = module.labels.id
  lambda_role_name      = "${local.name}-lambda-ecs-scheduler"
  eventbridge_role_name = "${local.name}-eventbridge-ecs-scheduler"

  # Define the ECS cluster ARN
  ecs_cluster_arn = "arn:aws-cn:ecs:cn-north-1:051953029949:cluster/msdpd-uatv-ecs-fargate"

  # Define the schedules for start and stop operations
  start_schedule = "cron(0 8 ? * MON-FRI *)"  # 8:00 AM Monday-Friday
  stop_schedule  = "cron(0 18 ? * MON-FRI *)" # 6:00 PM Monday-Friday

  # IAM policies for Lambda to manage ECS services
  lambda_policy_arns = [
    "arn:aws-cn:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy",
    "arn:aws-cn:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
    "arn:aws-cn:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole",
  ]

  # Lambda function code as a string
  lambda_function_code = <<EOF
import boto3
import logging
import os
from datetime import datetime

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

def lambda_handler(event, context):
    """
    Lambda function to start or stop all services in an ECS cluster.

    The function determines whether to start or stop services based on the 'action' parameter
    in the event. If not specified, it will determine the action based on the current time.

    Parameters:
    - event (dict): Lambda event data, can contain:
        - action (str, optional): 'start' or 'stop'. If not provided, will be determined by time.
        - cluster (str, optional): ECS cluster ARN. If not provided, uses environment variable or default.
    - context (LambdaContext): Lambda context object

    Returns:
    - dict: Result of the operation
    """
    # Get the ECS cluster ARN from event, environment variable, or use default
    cluster_arn = event.get('cluster',
                           os.environ.get('ECS_CLUSTER_ARN',
                                         'arn:aws-cn:ecs:cn-north-1:051953029949:cluster/msdpd-uatv-ecs-fargate'))

    # Determine action (start or stop) from event or based on time
    action = event.get('action')

    # If action not specified, determine based on time (example: stop after hours, start during work hours)
    if not action:
        current_hour = datetime.now().hour
        # Assuming work hours are 8 AM to 6 PM (8-18)
        if 8 <= current_hour < 18:
            action = 'start'
        else:
            action = 'stop'

    logger.info(f"Executing {action} action on cluster {cluster_arn}")

    # Initialize ECS client
    ecs_client = boto3.client('ecs', region_name='cn-north-1')

    try:
        # List all services in the cluster
        services = []
        next_token = None

        while True:
            if next_token:
                response = ecs_client.list_services(cluster=cluster_arn, nextToken=next_token)
            else:
                response = ecs_client.list_services(cluster=cluster_arn)

            services.extend(response['serviceArns'])

            if 'nextToken' in response:
                next_token = response['nextToken']
            else:
                break

        if not services:
            logger.info(f"No services found in cluster {cluster_arn}")
            return {
                'statusCode': 200,
                'body': f"No services found in cluster {cluster_arn}"
            }

        logger.info(f"Found {len(services)} services in cluster {cluster_arn}")

        # Process services in batches (ECS API limits to 10 services per call)
        batch_size = 10
        results = []

        for i in range(0, len(services), batch_size):
            batch = services[i:i + batch_size]

            # Get current service details
            service_details = ecs_client.describe_services(
                cluster=cluster_arn,
                services=batch
            )

            for service in service_details['services']:
                service_name = service['serviceName']
                current_count = service['desiredCount']

                if action == 'stop' and current_count > 0:
                    # Stop the service by setting desired count to 0
                    ecs_client.update_service(
                        cluster=cluster_arn,
                        service=service_name,
                        desiredCount=0
                    )
                    logger.info(f"Stopped service {service_name} (reduced from {current_count} to 0)")
                    results.append(f"Stopped {service_name}")

                elif action == 'start' and current_count == 0:
                    # Start the service by setting desired count to 1
                    # Note: This assumes the service should have at least 1 task
                    # For production, you might want to store the original count before stopping
                    ecs_client.update_service(
                        cluster=cluster_arn,
                        service=service_name,
                        desiredCount=1
                    )
                    logger.info(f"Started service {service_name} (increased from 0 to 1)")
                    results.append(f"Started {service_name}")

                else:
                    logger.info(f"Service {service_name} already in desired state (count: {current_count})")
                    results.append(f"No change for {service_name}")

        return {
            'statusCode': 200,
            'body': f"Successfully processed {len(services)} services. Results: {', '.join(results)}"
        }

    except Exception as e:
        logger.error(f"Error processing ECS services: {str(e)}")
        return {
            'statusCode': 500,
            'body': f"Error: {str(e)}"
        }
EOF
}

module "cron" {
  source            = "artifacts.merck.com/terraform-iac-shared__patterns/generic/aws"
  version           = "3.1.1"
  use_global_vpc_id = true
  global_vpc_id     = var.vpc_id

  lambda = {
    "${local.name}-ecs-scheduler" = {
      create_role            = true
      store_on_s3            = false
      create_package         = false
      publish                = true
      handler                = "main.lambda_handler"
      runtime                = "python3.12"
      timeout                = "300"
      vpc_subnet_ids         = var.vpc_subnet_ids
      local_existing_package = data.archive_file.lambda_scheduler_zip.output_path

      role_name = local.lambda_role_name

      # Environment variables
      environment_variables = {
        ECS_CLUSTER_ARN = local.ecs_cluster_arn
      }

      # Attach necessary policies
      attach_policies    = true
      number_of_policies = length(local.lambda_policy_arns)
      policies           = local.lambda_policy_arns

      # Additional policy statements for ECS service management
      attach_policy_statements = true
      policy_statements = {
        ecs_management = {
          effect    = "Allow",
          actions   = [
            "ecs:ListServices",
            "ecs:DescribeServices",
            "ecs:UpdateService"
          ],
          resources = ["*"]
        }
      }

      # Lambda trigger
      create_unqualified_alias_allowed_triggers = true
      create_current_version_allowed_triggers   = false
      allowed_triggers = {
        ExecutionFromEvents = {
          statement_id = "AllowExecutionFromEvents"
          action       = "lambda:InvokeFunction"
          service      = "events"
        }
      }

      tags = var.tags
    }
  }

  lambda_security_group = {
    "${local.name}-ecs-scheduler" = {
      description  = "Security group for ECS scheduler Lambda function"
      egress_rules = ["all-all"]
    }
  }

  eventbridge = {
    "${local.name}-ecs-scheduler" = {
      create_bus = false
      role_name  = local.eventbridge_role_name

      create_rules = true
      rules = {
        "${local.name}-start-ecs" = {
          description         = "Trigger Lambda to start ECS services"
          schedule_expression = local.start_schedule
        },
        "${local.name}-stop-ecs" = {
          description         = "Trigger Lambda to stop ECS services"
          schedule_expression = local.stop_schedule
        }
      }

      targets = {
        "${local.name}-start-ecs" = [
          {
            name  = "${local.name}-start-ecs"
            arn   = module.cron.lambda["${local.name}-ecs-scheduler"].lambda_function_arn
            input = jsonencode({
              action  = "start"
              cluster = local.ecs_cluster_arn
            })
          }
        ],
        "${local.name}-stop-ecs" = [
          {
            name  = "${local.name}-stop-ecs"
            arn   = module.cron.lambda["${local.name}-ecs-scheduler"].lambda_function_arn
            input = jsonencode({
              action  = "stop"
              cluster = local.ecs_cluster_arn
            })
          }
        ]
      }
    }
  }

  default_tags = var.default_tags
}
