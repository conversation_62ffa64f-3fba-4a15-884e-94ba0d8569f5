locals {
  replica_bucket_name = "${module.labels.id}-replica"
}

data "aws_partition" "current" {}
###############################
# Labels Module               #
###############################
module "labels" {
  source  = "artifacts.merck.com/terraform-iac-shared__internal/labels/aws"
  version = "~> 4.1"
  enabled = true

  appname     = var.resource_vars.appname
  region      = var.resource_vars.region
  attributes  = var.resource_vars.attributes
  label_order = var.resource_vars.label_order
  environment = var.resource_vars.environment
}


################################
# S3 Module - Source Bucket    #
################################
#tfsec:ignore:AWS052:exp:2023-05-01
module "s3_bucket" {
  # Update this source to use the Artifactory URL of a released version of the module: https://go.merck.com/iacreleases
  source                                   = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/s3-bucket/aws"
  version                                  = "3.3.0"
  create_bucket                            = true
  bucket                                   = module.labels.id
  policy                                   = data.aws_iam_policy_document.s3_source_bucket_policy.json
  cors_rule                                = var.s3_bucket_cors_rule
  lifecycle_rule                           = var.s3_bucket_lifecycle_rule
  intelligent_tiering                      = var.s3_bucket_intelligent_tiering
  tags                                     = var.tags
  default_tags                             = var.default_tags
  attach_deny_incorrect_encryption_headers = var.attach_deny_incorrect_encryption_headers
  attach_deny_incorrect_kms_key_sse        = var.attach_deny_incorrect_kms_key_sse
  allowed_kms_key_arn                      = var.allowed_kms_key_arn
  attach_deny_unencrypted_object_uploads   = var.attach_deny_unencrypted_object_uploads
  versioning                               = var.s3_bucket_versioning
  attach_policy                            = var.attach_elb_log_delivery_policy || var.attach_lb_log_delivery_policy || var.attach_policy
  website                                  = var.website
  kms_key_id                               = (var.kms_source_key_id != "" ? var.kms_source_key_id : module.aws_kms_key.key_arn)
  sse_algorithm                            = var.sse_algorithm
  force_destroy                            = var.force_destroy
  s3_log_bucket                            = "${module.labels.id}-log-bucket"
  depends_on = [
    module.replica_bucket.s3_bucket_arn
  ]
  replication_configuration = {
    role = aws_iam_role.replication.arn
    rules = [
      {
        id       = "${module.labels.id}-replication"
        status   = true
        priority = 2

        delete_marker_replication = true

        source_selection_criteria = {
          replica_modifications = {
            status = "Enabled"
          }
          sse_kms_encrypted_objects = {
            enabled = true
          }
        }

        # filter = {}

        destination = {
          bucket        = module.replica_bucket.s3_bucket_arn
          storage_class = "STANDARD"

          replica_kms_key_id = module.aws_kms_key_replica.key_arn
          account_id         = var.account_no

          access_control_translation = {
            owner = "Destination"
          }

          encryption_configuration = {
            replica_kms_key_id = local.kms_key_arn_replica
          }

          replication_time = {
            status  = "Enabled"
            minutes = 15
          }

          metrics = {
            status  = "Enabled"
            minutes = 15
          }
        }
      }
    ]
  }
  s3_log_force_destroy = true # set to true for cd job testing. This will cause the force deletion of objects within the bucket if any.
}



################################
# S3 Module - Replica Bucket    #
################################
#tfsec:ignore:AWS052:exp:2023-05-01
module "replica_bucket" {

  providers = {
    aws = aws.replica_region
  }
  source                    = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/s3-bucket/aws"
  version                   = "3.3.0"
  bucket                    = local.replica_bucket_name
  cors_rule                 = var.s3_bucket_cors_rule
  create_bucket             = true
  policy                    = data.aws_iam_policy_document.replica_bucket_policy.json
  lifecycle_rule            = var.s3_bucket_lifecycle_rule
  intelligent_tiering       = var.s3_bucket_intelligent_tiering
  tags                      = var.tags
  default_tags              = var.default_tags
  versioning                = var.s3_bucket_versioning
  attach_policy             = var.attach_elb_log_delivery_policy || var.attach_lb_log_delivery_policy || var.attach_policy
  website                   = var.website
  kms_key_id                = (var.kms_replica_key_id != "" ? var.kms_replica_key_id : module.aws_kms_key_replica.key_arn)
  sse_algorithm             = var.sse_algorithm
  force_destroy             = var.force_destroy
  s3_log_bucket             = "${module.labels.id}-replica-log-bucket"
  s3_log_force_destroy      = true # set to true for cd job testing. This will cause the force deletion of objects within the bucket if any.
  replication_configuration = {}
}


################################
# KMS Keys and Bucket Policies #
################################

module "aws_kms_key" {
  source             = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/kms/aws"
  version            = "~> 2.5"
  create             = (var.kms_source_key_id != null ? true : false)
  tags               = var.tags
  default_tags       = var.default_tags
  multi_region       = var.kms_use_multi_region
  key_administrators = ["arn:${data.aws_partition.current.partition}:iam::${var.account_no}:root", "arn:${data.aws_partition.current.partition}:iam::${var.account_no}:role/${var.deployment_role}"]
}

resource "aws_iam_role" "this" {
  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "ec2.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
  EOF
}

module "aws_kms_key_replica" {
  providers = {
    aws = aws.replica_region
  }
  source             = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/kms/aws"
  version            = "~> 2.5"
  create             = (var.kms_replica_key_id != null ? true : false)
  tags               = var.tags
  default_tags       = var.default_tags
  multi_region       = var.kms_use_multi_region
  aliases            = ["${module.labels.id}-s3replica"]
  key_administrators = ["arn:${data.aws_partition.current.partition}:iam::${var.account_no}:root", "arn:${data.aws_partition.current.partition}:iam::${var.account_no}:role/${var.deployment_role}"]
}

data "aws_iam_policy_document" "s3_source_bucket_policy" {


  statement {
    sid    = "AWSCloudTrailAclCheck20131101"
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["cloudtrail.amazonaws.com"]
    }

    actions = [
      "s3:GetBucketAcl",
    ]

    resources = [
      "arn:${data.aws_partition.current.partition}:s3:::${module.labels.id}*"
    ]
  }
  statement {
    sid    = "AWSCloudTrailWrite"
    effect = "Allow"

    principals {
      type        = "Service"
      identifiers = ["cloudtrail.amazonaws.com"]
    }

    actions = [
      "s3:PutObject",
    ]

    resources = [
      "arn:${data.aws_partition.current.partition}:s3:::${module.labels.id}*-replica"
    ]

    condition {
      test     = "StringEquals"
      variable = "s3:x-amz-acl"

      values = [
        "bucket-owner-full-control",
      ]
    }
  }
  statement {
    principals {
      type        = "AWS"
      identifiers = [aws_iam_role.this.arn]
    }

    actions = [
      "s3:ListBucket",
    ]

    resources = [
      "arn:${data.aws_partition.current.partition}:s3:::${module.labels.id}",
    ]
  }
}

data "aws_iam_policy_document" "replica_bucket_policy" {

  statement {
    sid    = "Stmt1644945277847"
    effect = "Allow"

    actions = [
      "s3:ReplicateObject",
      "s3:ReplicateTags",
      "s3:ObjectOwnerOverrideToBucketOwner",
      "s3:ReplicateDelete"
    ]

    resources = [
      "arn:${data.aws_partition.current.partition}:s3:::${module.labels.id}*-replica"
    ]
  }
  statement {
    principals {
      type        = "AWS"
      identifiers = [aws_iam_role.this.arn]
    }

    actions = [
      "s3:ListBucket",
    ]

    resources = [
      "arn:${data.aws_partition.current.partition}:s3:::${module.labels.id}*-replica",
    ]
  }
}
