# Changelog
​
All notable changes to this repository will be documented in this file.
​
The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

Please use the boilerplate code below to fill in information related to your change (create a new section for each new version).

## [Unreleased]

## [[1.4.1] - 2024-11-25](https://github.com/merck-gen/iac-shared-tf-module-aws-sqs-wrapper/releases/tag/1.4.1)


### Changed 
- pipeline url from jenkins to GitHub Actions in README.md
 
## [1.4.0] - 2024-05-29

### Added

- output variable:
    - `queue_arn_static` : The ARN of the SQS queue.
    - `dead_letter_queue_arn_static` : The ARN of the dead letter SQS queue.

### Changed

- source for `sqs-ext` from `4.1.1` to `4.2.0` in main.tf - [Ext PR](https://github.com/merck-gen/iac-shared-tf-module-aws-sqs-ext/pull/6)
- `default_tags` variable which now has a validation block

## [1.3.1] - 2024-03-27

### Changed

- source for `sqs-ext` from `4.1.0` to `4.1.1` in main.tf - [Ext PR](https://github.com/merck-gen/iac-shared-tf-module-aws-sqs-ext/pull/5)
    - Note: this increase in `sqs-ext` does not qualify for a minor version increase in this wrapper since new functionality was not introduced that affects this repo
- README.md now includes relevant service site link.

## [1.3.0] - 2024-01-09

### Changed

- adoption tags module version to `1.0.0` [changelog](https://github.com/merck-gen/iac-shared-tf-module-aws-adoption-tags-int/blob/main/CHANGELOG.md)
- `iac-shared-tf-module-aws-kms-wrapper` module version to `2.1.0`
- README.md

## [1.2.0] - 2023-11-17

### Changed
- README.md .
- adoption tags module version to `0.2.0`
- main.tf default_tags 

## [1.1.0] - 2023-11-02

### Added
- Variable `create_dlq_redrive_allow_policy` to add option to disable creation of redrive policy for the dead letter queue

### Changed
- main.tf `source` version for EXT from `4.0.2` to `4.1.0` (https://github.com/merck-gen/iac-shared-tf-module-aws-sqs-ext/pull/4)
- README.md files in root and examples folder

### Removed
- `name` variable in the complete example

## [1.0.4] - 2023-08-18

### Added
- consumption.tf
- `tags` module

### Changed 
- README.md
- main.tf
- versions.tf
  
## [1.0.3] - 2023-08-17

### Changed
- Changed module `source` version for EXT from `4.0.1` to `4.0.2`.

## [1.0.2] - 2023-07-03

### Changed
- Changed AWS provider version to `>= 5.0.1`.

### Added
- Added build badge.
- Updated README.
- Added KMS module in the example.

### Removed
- Removed KMS default values from variables.tf to make complaint
- Removed `Division` and `Costcenter` from label module.

## [0.1.0] - 2023-07-01

### Features

- Initial release of this newly created module.
- Placeholder bullet for important updates made in this version.
