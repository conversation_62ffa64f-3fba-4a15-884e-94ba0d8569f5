version: 0.1
resource_type_default_usage:
  #
  # Terraform AWS resources
  # This includes all available usage parameters published by Infracost - https://github.com/infracost/infracost/blob/master/infracost-usage-example.yml
  #
  aws_acmpca_certificate_authority:
    monthly_requests: 20000 # Monthly private certificate requests.

  aws_api_gateway_rest_api:
    monthly_requests:  ********* # Monthly requests to the Rest API Gateway.

  aws_apigatewayv2_api:
    monthly_requests: *********       # Monthly requests to the HTTP API Gateway.
    request_size_kb: 512              # Average request size sent to the HTTP API Gateway in KB. Requests are metered in 512KB increments, maximum size is 10MB.
    monthly_messages: **********      # Monthly number of messages sent to the Websocket API Gateway.
    message_size_kb: 32               # Average size of the messages sent to the Websocket API Gateway in KB. Messages are metered in 32 KB increments, maximum size is 128KB.
    monthly_connection_mins: ******** # Monthly total connection minutes to Websockets.

  aws_autoscaling_group:
    instances: 15 # Number of instances in the autoscaling group.
    operating_system: linux # Override the operating system of the instance, can be: linux, windows, suse, rhel.
    reserved_instance_type: standard # Offering class for Reserved Instances, can be: convertible, standard.
    reserved_instance_term: 1_year # Term for Reserved Instances, can be: 1_year, 3_year.
    reserved_instance_payment_option: no_upfront # Payment option for Reserved Instances, can be: no_upfront, partial_upfront, all_upfront.
    monthly_cpu_credit_hrs: 350 # Number of hours in the month where the instance is expected to burst. Only applicable with t2, t3 & t4 Instance types. T2 requires credit_specification to be unlimited.
    vcpu_count: 2 # Number of the vCPUs for the instance type. Only applicable with t2, t3 & t4 Instance types. T2 requires credit_specification to be unlimited.

  aws_backup_vault:
    monthly_efs_warm_restore_gb: 10000 # Monthly number of EFS warm restore in GB.
    monthly_efs_cold_restore_gb: 10000 # Monthly number of EFS cold restore in GB.
    monthly_efs_item_restore_requests: 10000 # Monthly number of EFS item-level restore requests.
    monthly_efs_warm_backup_gb: 10000 # Monthly number of EFS warm backups in GB.
    monthly_efs_cold_backup_gb: 10000 # Monthly number of EFS cold backups in GB.
    monthly_ebs_snapshot_gb: 10000 # Monthly number of EBS snapshots in GB.
    monthly_rds_snapshot_gb: 10000 # Monthly number of RDS snapshots in GB.
    monthly_aurora_snapshot_gb: 10000 # Monthly number of Aurora snapshots in GB.
    monthly_dynamodb_backup_gb: 10000 # Monthly number of DynamoDB backups in GB.
    monthly_dynamodb_restore_gb: 10000 # Monthly number of DynamoDB restore in GB.
    monthly_fsx_windows_backup_gb: 10000 # Monthly number of FSX Windows backups in GB.
    monthly_fsx_lustre_backup_gb: 10000 # Monthly number of FSX Lustre backups in GB.

  aws_cloudformation_stack:
    monthly_handler_operations: 10000 # Monthly number of non-free handler operations (resources outside of the AWS::*, Alexa::*, and Custom::* namespaces).
    monthly_duration_secs: 0 # Monthly duration of non-free handler operations that go above 30 seconds, in seconds.

  aws_cloudformation_stack_set:
    monthly_handler_operations: 10000 # Monthly number of non-free handler operations (resources outside of the AWS::*, Alexa::*, and Custom::* namespaces).
    monthly_duration_secs: 0 # Monthly duration of non-free handler operations that go above 30 seconds, in seconds.

  aws_cloudtrail:
    monthly_additional_management_events: 100000 # Monthly additional copies of read and write management events. The first copy of management events per region is free, so this should only be non-zero if there are multiple trails recording management events in this region.
    monthly_data_events: 200000 # Monthly data events delivered to S3, Lambda or DynamoDB
    monthly_insight_events: 400000 # Monthly CloudTrail Insight events

  aws_cloudwatch_event_bus:
    monthly_custom_events: 1000000            # Monthly custom events published. Each 64 KB chunk of payload is billed as 1 event.
    monthly_third_party_events: 2000000       # Monthly third-party and cross-account events published. Each 64 KB chunk of payload is billed as 1 event.
    monthly_archive_processing_gb: 100        # Monthly archive event processing in GB.
    archive_storage_gb: 200                   # Archive storage used for event replay in GB.
    monthly_schema_discovery_events: 1000000  # Monthly events ingested for schema discovery. Each 8 KB chunk of payload is billed as 1 event.

  aws_cloudwatch_log_group:
    storage_gb: 1000               # Total data stored by CloudWatch logs in GB.
    monthly_data_ingested_gb: 1000 # Monthly data ingested by CloudWatch logs in GB.
    monthly_data_scanned_gb: 200   # Monthly data scanned by CloudWatch logs insights in GB.

  aws_codebuild_project:
    monthly_build_mins: 10000 # Monthly total duration of builds in minutes. Each build is rounded up to the nearest minute.

  aws_config_config_rule:
    monthly_rule_evaluations: 1000000 # Monthly config rule evaluations.

  aws_config_configuration_recorder:
    monthly_config_items: 10000        # Monthly config item records.
    monthly_custom_config_items: 20000 # Monthly custom config item records.

  aws_config_organization_custom_rule:
    monthly_rule_evaluations: 300000 # Monthly config rule evaluations.

  aws_config_organization_managed_rule:
    monthly_rule_evaluations: 10000 # Monthly config rule evaluations.

  aws_data_transfer:
    region: us-east-1                           # Region the data transfer is originating from.
    monthly_intra_region_gb: 1000               # Monthly data transferred between availability zones in the region. Infracost multiplies this by two to account for AWS charging in-bound and out-bound rates.
    monthly_outbound_us_east_to_us_east_gb: 500 # Monthly data transferred between US east regions. NOTE: this is only valid if the region is a us-east region.
    monthly_outbound_other_regions_gb: 750      # Monthly data transferred to other AWS regions.
    monthly_outbound_internet_gb: 5000          # Monthly data transferred to the Internet.

  aws_db_instance:
    additional_backup_storage_gb: 1000  # Amount of backup storage used that is in excess of 100% of the storage size for all databases in GB.
    monthly_standard_io_requests: 10000 # Monthly number of input/output requests for database.
    monthly_additional_performance_insights_requests: 10000 # Monthly Performance Insights API requests above the 1000000 requests included in the free tier.
    reserved_instance_term: 1_year                          # Term for Reserved Instances, can be: 1_year, 3_year.
    reserved_instance_payment_option: partial_upfront       # Payment option for Reserved Instances, can be: no_upfront (only for 1_year term), partial_upfront, all_upfront.

  aws_directory_service_directory:
    additional_domain_controllers: 3 # The number of domain controllers in the directory service provisioned in addition to the minimum 2 controllers
    shared_accounts: 8 # Number of accounts that Microsoft AD directory is shared with

  aws_docdb_cluster:
    backup_storage_gb: 10000      # Amount of backup storage that is in excess of 100% of the storage size for the cluster in GB.

  aws_docdb_cluster_instance:
    data_storage_gb: 1000         # Total storage for cluster in GB.
    monthly_io_requests: ********* # Monthly number of input/output requests for cluster.
    monthly_cpu_credit_hrs: 100 # Monthly CPU credits used over the instance baseline in vCPU-hours, only applicable for T3 instances.

  aws_docdb_cluster_snapshot:
    backup_storage_gb: 10000      # Amount of backup storage that is in excess of 100% of the storage size for the cluster in GB.

  aws_dx_connection:
    monthly_outbound_region_to_dx_location_gb: 100 # (DEPRECATED use monthly_outbound_from_region_to_dx_connection_location instead) Monthly outbound data transferred from AWS region to DX location in GB.
    monthly_outbound_from_region_to_dx_connection_location:
      us_east_1: 200 # Monthly outbound data transferred to the DX location from us-east-1 in GB
      eu_west_2: 100 # Monthly outbound data transferred to the DX location from eu-west-2 in GB
    dx_virtual_interface_type: private             # Interface type impacts outbound data transfer costs over DX, can be: private, public.
    dx_connection_type: dedicated                  # Connection type impacts the per-port hourly price, can be: dedicated, hosted.

  aws_dx_gateway_association:
    monthly_data_processed_gb: 100 # Monthly data processed by the DX gateway association per month in GB.

  aws_dynamodb_table:
    monthly_write_request_units: 3000000  # Monthly write request units in (used for on-demand DynamoDB).
    monthly_read_request_units: 8000000   # Monthly read request units in (used for on-demand DynamoDB).
    storage_gb: 230                       # Total storage for tables in GB.
    pitr_backup_storage_gb: 2300          # Total storage for Point-In-Time Recovery (PITR) backups in GB.
    on_demand_backup_storage_gb: 460      # Total storage for on-demand backups in GB.
    monthly_data_restored_gb: 230         # Monthly size of restored data in GB.
    monthly_streams_read_request_units: 2 # Monthly streams read request units.

  aws_ebs_snapshot:
    monthly_list_block_requests: 1000000  # Monthly number of ListChangedBlocks and ListSnapshotBlocks requests.
    monthly_get_block_requests: 100000    # Monthly number of GetSnapshotBlock requests (block size is 512KiB).
    monthly_put_block_requests: 100000    # Monthly number of PutSnapshotBlock requests (block size is 512KiB).
    fast_snapshot_restore_hours: 100      # Monthly number of DSU-hours for Fast snapshot restore

  aws_ebs_volume:
    monthly_standard_io_requests: ******** # Monthly I/O requests for standard volume (Magnetic storage).

  aws_ec2_host:
    reserved_instance_term: 1_year # Term for Reserved Instances, can be: 1_year, 3_year.
    reserved_instance_payment_option: partial_upfront # Payment option for Reserved Instances, can be: no_upfront, partial_upfront, all_upfront.

  aws_ec2_transit_gateway_vpc_attachment:
    monthly_data_processed_gb: 100 # Monthly data processed by the EC2 transit gateway attachment(s) in GB.

  aws_ecr_repository:
    storage_gb: 1 # Total size of ECR repository in GB.

  aws_efs_file_system:
    storage_gb: 230                         # Total storage for Standard class in GB.
    infrequent_access_storage_gb: 100       # Total storage for Infrequent Access class in GB.
    monthly_infrequent_access_read_gb: 50   # Monthly infrequent access read requests in GB.
    monthly_infrequent_access_write_gb: 100 # Monthly infrequent access write requests in GB.

  aws_eks_node_group:
    instances: 15 # Number of instances in the EKS node group.
    operating_system: linux # Override the operating system of the instance, can be: linux, windows, suse, rhel.
    reserved_instance_type: standard # Offering class for Reserved Instances, can be: convertible, standard.
    reserved_instance_term: 1_year # Term for Reserved Instances, can be: 1_year, 3_year.
    reserved_instance_payment_option: partial_upfront # Payment option for Reserved Instances, can be: no_upfront, partial_upfront, all_upfront.
    monthly_cpu_credit_hrs: 350 # Number of hours in the month where the instance is expected to burst. Only applicable with t2, t3 & t4 Instance types. T2 requires credit_specification to be unlimited.
    vcpu_count: 2 # Number of the vCPUs for the instance type. Only applicable with t2, t3 & t4 Instance types. T2 requires credit_specification to be unlimited.

  aws_elastic_beanstalk_environment:
    db:
      additional_backup_storage_gb: 1000  # Amount of backup storage used that is in excess of 100% of the storage size for all databases in GB.
      monthly_standard_io_requests: 10000 # Monthly number of input/output requests for database.
      monthly_additional_performance_insights_requests: 10000 # Monthly Performance Insights API requests above the 1000000 requests included in the free tier.
    ec2:
      monthly_cpu_credit_hrs: 350 # Number of hours in the month where the instance is expected to burst. Only applicable with t2, t3 & t4 Instance types. T2 requires credit_specification to be unlimited.
    cloudwatch:
      storage_gb: 1000               # Total data stored by CloudWatch logs in GB.
      monthly_data_ingested_gb: 1000 # Monthly data ingested by CloudWatch logs in GB.
      monthly_data_scanned_gb: 200   # Monthly data scanned by CloudWatch logs insights in GB.
    lb:
      new_connections: 500000    # Number of newly established connections per second on average.
      active_connections: 100000 # Number of active connections per minute on average.
      processed_bytes_gb: 25000  # The number of bytes processed by the load balancer for HTTP(S) requests and responses in GB.
      rule_evaluations: 10000    # Number of rule evaluations on application/network load balancers
    elb:
      monthly_data_processed_gb: 10000 # Monthly data processed on classic loadbalaner

  aws_elasticache_cluster:
    snapshot_storage_size_gb: 10000 # Size of Redis snapshots in GB.
    reserved_instance_term: 1_year                          # Term for Reserved Instances, can be: 1_year, 3_year.
    reserved_instance_payment_option: partial_upfront       # Payment option for Reserved Instances. Can be: no_upfront, partial_upfront, all_upfront for standard offering class. Can be: heavy_utilization, medium_utilization, light utilization for legacy offering class.

  aws_elasticache_replication_group:
    snapshot_storage_size_gb: 10000 # Size of Redis snapshots in GB.
    reserved_instance_term: 1_year                          # Term for Reserved Instances, can be: 1_year, 3_year.
    reserved_instance_payment_option: partial_upfront       # Payment option for Reserved Instances. Can be: no_upfront, partial_upfront, all_upfront for standard offering class. Can be: heavy_utilization, medium_utilization, light utilization for legacy offering class.

  aws_elb:
    monthly_data_processed_gb: 10000 # Monthly data processed by a Classic Load Balancer in GB.

  aws_globalaccelerator_endpoint_group:
    monthly_inbound_data_transfer_gb:
      us: 12340000 # United States, Mexico, Canada
      europe: 22340000 # Europe
      south_africa: 32340000 # South Africa, Kenya
      south_america: 42340000 # South America
      south_korea: 52340000 # South Korea
      australia: 62340000 # Australia, New Zealand
      asia_pacific: 62340000 # Asia Pacific
      india: 72340000 # India, Indonesia, Philippines, Thailand
    monthly_outbound_data_transfer_gb:
      us: 1234 # United States, Mexico, Canada
      europe: 2234 # Europe
      south_africa: 3234 # South Africa, Kenya
      south_america: 4234 # South America
      south_korea: 5234 # South Korea
      australia: 6234 # Australia, New Zealand
      asia_pacific: 6234 # Asia Pacific
      india: 7234 # India, Indonesia, Philippines, Thailand

  aws_glue_catalog_database:
    monthly_objects: 100000  # Monthly number of objects stored above the free one million object storage limit.
    monthly_requests: 100000 # Monthly number of requests to the db beyond the free one million requests limit.

  aws_glue_crawler:
    monthly_hours: 60 # Monthly number of hours the Glue crawler ran for.

  aws_glue_job:
    monthly_hours: 60 # Monthly number of hours the Glue job ran for.

  aws_instance:
    operating_system: linux # Override the operating system of the instance, can be: linux, windows, suse, rhel.
    reserved_instance_type: standard # Offering class for Reserved Instances, can be: convertible, standard.
    reserved_instance_term: 1_year # Term for Reserved Instances, can be: 1_year, 3_year.
    reserved_instance_payment_option: all_upfront # Payment option for Reserved Instances, can be: no_upfront, partial_upfront, all_upfront.
    monthly_cpu_credit_hrs: 350 # Number of hours in the month where the instance is expected to burst. Only applicable with t2, t3 & t4 Instance types. T2 requires credit_specification to be unlimited.
    vcpu_count: 2 # Number of the vCPUs for the instance type. Only applicable with t2, t3 & t4 Instance types. T2 requires credit_specification to be unlimited.
    monthly_hrs: 450 # Monthly number of hours the instance ran for.

  aws_fsx_windows_file_system:
    backup_storage_gb: 10000 # Total storage used for backups in GB.

  aws_kinesis_analytics_application:
    kinesis_processing_units: 10 # Number of Kinesis processing units.
    durable_application_backup_gb: 100 # Total amount of durable application backup in GB.

  aws_kinesisanalyticsv2_application:
    kinesis_processing_units: 10 # Number of Kinesis processing units.
    durable_application_backup_gb: 100 # Total amount of durable application backup in GB.

  aws_kinesisanalyticsv2_application_snapshot:
    durable_application_backup_gb: 100 # Total amount of durable application backups in GB.

  aws_kinesis_firehose_delivery_stream:
    monthly_data_ingested_gb: 3000000 # Monthly data ingested by the Delivery Stream in GB.

  aws_kinesis_stream:
    monthly_on_demand_data_in_gb: 0.20 # Monthly data ingested by the stream in GB.
    monthly_on_demand_data_out_gb: 0.20 # Monthly data egressed by the stream in GB total, (not per consumer application).
    monthly_on_demand_efo_data_out_gb: 2.0 # Monthly data egressed by the stream in GB to EFO consumers.
    monthly_on_demand_extended_retention_gb: 1.0 # Monthly data stored by the stream in GB that exceeds the 24 hour retention period but is less than 7 days.
    monthly_on_demand_long_term_retention_gb: 1.0 # Monthly data stored by the stream in GB that exceeds the 7 day retention period.
    monthly_provisioned_put_units: 6500000 # Monthly provisioned put units for the stream.
    monthly_provisioned_extended_retention_gb: 100 # Monthly data stored by the stream in GB that exceeds the 24 hour retention period but is less than 7 days.
    monthly_provisioned_long_term_retention_gb: 100 # Monthly data stored by the stream in GB that exceeds the 7 day retention period.
    monthly_provisioned_long_term_retrieval_gb: 100 # Monthly data retrieved by the stream in GB that exceeds the 7 day retention period.
    monthly_provisioned_efo_data_out_gb: 100 # Monthly data egressed by the stream in GB to EFO consumers.
    monthly_provisioned_efo_consumer_hours: 100 # Monthly hours of EFO consumer application usage.

  aws_lambda_function:
    monthly_requests: 100000 # Monthly requests to the Lambda function.
    request_duration_ms: 500 # Average duration of each request in milliseconds.

  aws_lambda_provisioned_concurrency_config:
    monthly_duration_hrs: 100 # Number of hours in a month that provisioned concurrency will be enabled.
    request_duration_ms: 350 # Average duration of each request in milliseconds during the enabled period.
    monthly_requests: ******** # Number of requests sent to the function during the enabled period.
    architecture: arm64 # Architecture of the Lambda function.
    memory_mb: 512 # Memory size of the Lambda function.

  aws_alb:
    new_connections: 10000    # Number of newly established connections per second on average.
    active_connections: 10000 # Number of active connections per minute on average.
    processed_bytes_gb: 1000  # The number of bytes processed by the load balancer for HTTP(S) requests and responses in GB.
    rule_evaluations: 10000   # The product of number of rules processed by the load balancer and the request rate.

  aws_lb:
    new_connections: 10000    # Number of newly established connections per second on average.
    active_connections: 10000 # Number of active connections per minute on average.
    processed_bytes_gb: 1000  # The number of bytes processed by the load balancer for HTTP(S) requests and responses in GB.
    rule_evaluations: 10000   # The product of number of rules processed by the load balancer and the request rate.

  aws_nat_gateway:
    monthly_data_processed_gb: 10 # Monthly data processed by the NAT Gateway in GB.

  aws_neptune_cluster:
    storage_gb: 100                # Total storage for the cluster in GB.
    monthly_io_requests: ********  # Monthly number of input/output requests for cluster.
    backup_storage_gb: 1000        # Total storage used for backups in GB.

  aws_neptune_cluster_instance:
    monthly_cpu_credit_hrs: 10     # Number of hours in a month, where you expect to burst the baseline credit balance of a "t3" instance type.

  aws_neptune_cluster_snapshot:
    backup_storage_gb: 1000        # Total storage used for backup snapshots in GB.

  aws_networkfirewall_firewall:
    monthly_data_processed_gb: 100 # Monthly data processed by the Network Firewall in GB.

  aws_mq_broker:
    storage_size_gb: 12 # Data storage per instance in GB.

  aws_mwaa_environment:
    additional_workers: 2.5        # Average number of monthly additional worker instances
    additional_schedulers: 2       # Average number of monthly additional scheduler instances
    meta_database_gb: 1000         # Total storage used for meta database

  aws_rds_cluster:
    capacity_units_per_hr: 50          # Number of aurora capacity units per hour. Only used when engine_mode is "serverless"
    storage_gb: 200                    # Storage amount in GB allocated to the aurora cluster.
    write_requests_per_sec: 100        # Total number of reads per second for the cluster.
    read_requests_per_sec: 100         # Total number of writes per second for the cluster.
    backup_snapshot_size_gb: 200       # Individual storage size for backup snapshots, used in conjunction with resource parameter "backup_retention_period".
    average_statements_per_hr: 10000   # Number of statements generated per hour when backtrack is enabled. Only available for MySQl-compatible Aurora
    change_records_per_statement: 0.38 # Records changed per statement executed.
    backtrack_window_hrs: 24           # The duration window for which Aurora will support rewinding the DB cluster to a specific point in time.
    snapshot_export_size_gb: 200       # Size of snapshot that's exported to s3 in parquet format.

  aws_rds_cluster_instance:
    monthly_cpu_credit_hrs: 24   # Number of hours in a month, where you expect to burst the baseline credit balance of a "t3" instance type.
    vcpu_count: 2 # # (DEPRECATED this is now calculated automatically) Number of virtual CPUs allocated to your "t3" instance type. Currently instances with 2 vCPUs are available.
    monthly_additional_performance_insights_requests: 10000 # Monthly Performance Insights API requests above the 1000000 requests included in the free tier.
    reserved_instance_term: 1_year                          # Term for Reserved Instances, can be: 1_year, 3_year.
    reserved_instance_payment_option: partial_upfront       # Payment option for Reserved Instances, can be: no_upfront (only for 1_year term), partial_upfront, all_upfront.

  aws_redshift_cluster:
    managed_storage_gb: 10000
    excess_concurrency_scaling_secs: 20000
    spectrum_data_scanned_tb: 1.5
    backup_storage_gb: 1000000

  aws_route53_health_check:
    endpoint_type: aws # Type of health check endpoint to query, can be: aws, non_aws.

  aws_route53_record:
    monthly_standard_queries: 1*********      # Monthly number of Standard queries.
    monthly_latency_based_queries: ********** # Monthly number of Latency Based Routing queries.
    monthly_geo_queries: **********           # Monthly number of Geo DNS and Geoproximity queries.

  aws_route53_resolver_endpoint:
    monthly_queries: *********** # Monthly number of DNS queries processed through the endpoints.

  aws_s3_bucket_analytics_configuration:
    monthly_monitored_objects: ******** # Monthly number of monitored objects by S3 Analytics Storage Class Analysis.

  aws_s3_bucket_inventory:
    monthly_listed_objects: ********* # Monthly number of listed objects.

  aws_s3_bucket_lifecycle_configuration:
    object_tags: ******** # Total object tags.
    standard: # Usages of S3 Standard:
      storage_gb: 10000                     # Total storage in GB.
      monthly_tier_1_requests: 1000000      # Monthly PUT, COPY, POST, LIST requests (Tier 1).
      monthly_tier_2_requests: 100000       # Monthly GET, SELECT, and all other requests (Tier 2).
      monthly_select_data_scanned_gb: 10000 # Monthly data scanned by S3 Select in GB.
      monthly_select_data_returned_gb: 1000 # Monthly data returned by S3 Select in GB.
    intelligent_tiering: # Usages of S3 Intelligent - Tiering:
      frequent_access_storage_gb: 20000             # Total storage for Frequent Access Tier in GB.
      infrequent_access_storage_gb: 20000           # Total storage for Infrequent Access Tier in GB.
      monitored_objects: 2000                       # Total objects monitored by the Intelligent Tiering.
      monthly_tier_1_requests: 2000000              # Monthly PUT, COPY, POST, LIST requests (Tier 1).
      monthly_tier_2_requests: 200000               # Monthly GET, SELECT, and all other requests (Tier 2).
      monthly_lifecycle_transition_requests: 200000 # Monthly Lifecycle Transition requests.
      monthly_select_data_scanned_gb: 20000         # Monthly data scanned by S3 Select in GB.
      monthly_select_data_returned_gb: 2000         # Monthly data returned by S3 Select in GB.
      early_delete_gb: 200000                       # If an archive is deleted within 1 months of being uploaded, you will be charged an early deletion fee per GB.
    standard_infrequent_access: # Usages of S3 Standard - Infrequent Access:
      storage_gb: 30000                             # Total storage in GB.
      monthly_tier_1_requests: 3000000              # Monthly PUT, COPY, POST, LIST requests (Tier 1).
      monthly_tier_2_requests: 300000               # Monthly GET, SELECT, and all other requests (Tier 2).
      monthly_lifecycle_transition_requests: 300000 # Monthly Lifecycle Transition requests.
      monthly_data_retrieval_gb: 30000              # Monthly data retrievals in GB
      monthly_select_data_scanned_gb: 30000         # Monthly data scanned by S3 Select in GB.
      monthly_select_data_returned_gb: 3000         # Monthly data returned by S3 Select in GB.
    one_zone_infrequent_access: # Usages of S3 One Zone - Infrequent Access:
      storage_gb: 40000                             # Total storage in GB.
      monthly_tier_1_requests: 4000000              # Monthly PUT, COPY, POST, LIST requests (Tier 1).
      monthly_tier_2_requests: 400000               # Monthly GET, SELECT, and all other requests (Tier 2).
      monthly_lifecycle_transition_requests: 400000 # Monthly Lifecycle Transition requests.
      monthly_data_retrieval_gb: 40000              # Monthly data retrievals in GB
      monthly_select_data_scanned_gb: 40000         # Monthly data scanned by S3 Select in GB.
      monthly_select_data_returned_gb: 4000         # Monthly data returned by S3 Select in GB.
    glacier_flexible_retrieval: # Usages of S3 Glacier Flexible Retrieval:
      storage_gb: 50000                                 # Total storage in GB.
      monthly_tier_1_requests: 5000000                  # Monthly PUT, COPY, POST, LIST requests (Tier 1).
      monthly_tier_2_requests: 500000                   # Monthly GET, SELECT, and all other requests (Tier 2).
      monthly_lifecycle_transition_requests: 500000     # Monthly Lifecycle Transition requests.
      monthly_standard_select_data_scanned_gb: 500000   # Monthly data scanned by S3 Select in GB (for standard level of S3 Glacier).
      monthly_standard_select_data_returned_gb: 500000  # Monthly data returned by S3 Select in GB (for standard level of S3 Glacier).
      monthly_bulk_select_data_scanned_gb: 500000       # Monthly data scanned by S3 Select in GB (for bulk level of S3 Glacier)
      monthly_bulk_select_data_returned_gb: 500000      # Monthly data returned by S3 Select in GB (for bulk level of S3 Glacier)
      monthly_expedited_select_data_scanned_gb: 500000  # Monthly data scanned by S3 Select in GB (for expedited level of S3 Glacier)
      monthly_expedited_select_data_returned_gb: 500000 # Monthly data returned by S3 Select in GB (for expedited level of S3 Glacier)
      monthly_standard_data_retrieval_requests: 500000  # Monthly data Retrieval requests (for standard level of S3 Glacier).
      monthly_expedited_data_retrieval_requests: 500000 # Monthly data Retrieval requests (for expedited level of S3 Glacier).
      monthly_standard_data_retrieval_gb: 5000          # Monthly data retrievals in GB (for standard level of S3 Glacier).
      monthly_expedited_data_retrieval_gb: 5000         # Monthly data retrievals in GB (for expedited level of S3 Glacier).
      early_delete_gb: 500000                           # If an archive is deleted within 3 months of being uploaded, you will be charged an early deletion fee per GB.
    glacier_deep_archive: # Usages of S3 Glacier Deep Archive:
      storage_gb: 60000                                # Total storage in GB.
      monthly_tier_1_requests: 6000000                 # Monthly PUT, COPY, POST, LIST requests (Tier 1).
      monthly_tier_2_requests: 600000                  # Monthly GET, SELECT, and all other requests (Tier 2).
      monthly_lifecycle_transition_requests: 600000    # Monthly Lifecycle Transition requests.
      monthly_standard_data_retrieval_requests: 600000 # Monthly data Retrieval requests (for standard level of S3 Glacier).
      monthly_bulk_data_retrieval_requests: 600000     # Monthly data Retrieval requests (for bulk level of S3 Glacier).
      monthly_standard_data_retrieval_gb: 6000         # Monthly data retrievals in GB (for standard level of S3 Glacier).
      monthly_bulk_data_retrieval_gb: 6000             # Monthly data retrievals in GB (for bulk level of S3 Glacier).
      early_delete_gb: 600000                          # If an archive is deleted within 6 months of being uploaded, you will be charged an early deletion fee per GB.

  aws_s3_bucket:
    object_tags: ******** # Total object tags. Only for AWS provider V3.
    standard: # Usages of S3 Standard:
      storage_gb: 10000 # Total storage in GB.
      monthly_tier_1_requests: 1000000 # Monthly PUT, COPY, POST, LIST requests (Tier 1).
      monthly_tier_2_requests: 100000 # Monthly GET, SELECT, and all other requests (Tier 2).
      monthly_select_data_scanned_gb: 10000 # Monthly data scanned by S3 Select in GB.
      monthly_select_data_returned_gb: 1000 # Monthly data returned by S3 Select in GB.
    intelligent_tiering: # Usages of S3 Intelligent - Tiering:
      frequent_access_storage_gb: 20000 # Total storage for Frequent Access Tier in GB.
      infrequent_access_storage_gb: 20000 # Total storage for Infrequent Access Tier in GB.
      monitored_objects: 2000 # Total objects monitored by the Intelligent Tiering.
      monthly_tier_1_requests: 2000000 # Monthly PUT, COPY, POST, LIST requests (Tier 1).
      monthly_tier_2_requests: 200000 # Monthly GET, SELECT, and all other requests (Tier 2).
      monthly_lifecycle_transition_requests: 200000 # Monthly Lifecycle Transition requests.
      monthly_select_data_scanned_gb: 20000 # Monthly data scanned by S3 Select in GB.
      monthly_select_data_returned_gb: 2000 # Monthly data returned by S3 Select in GB.
      early_delete_gb: 200000 # If an archive is deleted within 1 months of being uploaded, you will be charged an early deletion fee per GB.
    standard_infrequent_access: # Usages of S3 Standard - Infrequent Access:
      storage_gb: 30000 # Total storage in GB.
      monthly_tier_1_requests: 3000000 # Monthly PUT, COPY, POST, LIST requests (Tier 1).
      monthly_tier_2_requests: 300000 # Monthly GET, SELECT, and all other requests (Tier 2).
      monthly_lifecycle_transition_requests: 300000 # Monthly Lifecycle Transition requests.
      monthly_data_retrieval_gb: 30000 # Monthly data retrievals in GB
      monthly_select_data_scanned_gb: 30000 # Monthly data scanned by S3 Select in GB.
      monthly_select_data_returned_gb: 3000 # Monthly data returned by S3 Select in GB.
    one_zone_infrequent_access: # Usages of S3 One Zone - Infrequent Access:
      storage_gb: 40000 # Total storage in GB.
      monthly_tier_1_requests: 4000000 # Monthly PUT, COPY, POST, LIST requests (Tier 1).
      monthly_tier_2_requests: 400000 # Monthly GET, SELECT, and all other requests (Tier 2).
      monthly_lifecycle_transition_requests: 400000 # Monthly Lifecycle Transition requests.
      monthly_data_retrieval_gb: 40000 # Monthly data retrievals in GB
      monthly_select_data_scanned_gb: 40000 # Monthly data scanned by S3 Select in GB.
      monthly_select_data_returned_gb: 4000 # Monthly data returned by S3 Select in GB.
    glacier_flexible_retrieval: # Usages of S3 Glacier Flexible Retrieval:
      storage_gb: 50000 # Total storage in GB.
      monthly_tier_1_requests: 5000000 # Monthly PUT, COPY, POST, LIST requests (Tier 1).
      monthly_tier_2_requests: 500000 # Monthly GET, SELECT, and all other requests (Tier 2).
      monthly_lifecycle_transition_requests: 500000 # Monthly Lifecycle Transition requests.
      monthly_standard_select_data_scanned_gb: 500000 # Monthly data scanned by S3 Select in GB (for standard level of S3 Glacier).
      monthly_standard_select_data_returned_gb: 500000 # Monthly data returned by S3 Select in GB (for standard level of S3 Glacier).
      monthly_bulk_select_data_scanned_gb: 500000 # Monthly data scanned by S3 Select in GB (for bulk level of S3 Glacier)
      monthly_bulk_select_data_returned_gb: 500000 # Monthly data returned by S3 Select in GB (for bulk level of S3 Glacier)
      monthly_expedited_select_data_scanned_gb: 500000 # Monthly data scanned by S3 Select in GB (for expedited level of S3 Glacier)
      monthly_expedited_select_data_returned_gb: 500000 # Monthly data returned by S3 Select in GB (for expedited level of S3 Glacier)
      monthly_standard_data_retrieval_requests: 500000 # Monthly data Retrieval requests (for standard level of S3 Glacier).
      monthly_expedited_data_retrieval_requests: 500000 # Monthly data Retrieval requests (for expedited level of S3 Glacier).
      monthly_standard_data_retrieval_gb: 5000 # Monthly data retrievals in GB (for standard level of S3 Glacier).
      monthly_expedited_data_retrieval_gb: 5000 # Monthly data retrievals in GB (for expedited level of S3 Glacier).
      early_delete_gb: 500000 # If an archive is deleted within 3 months of being uploaded, you will be charged an early deletion fee per GB.
    glacier_deep_archive: # Usages of S3 Glacier Deep Archive:
      storage_gb: 60000 # Total storage in GB.
      monthly_tier_1_requests: 6000000 # Monthly PUT, COPY, POST, LIST requests (Tier 1).
      monthly_tier_2_requests: 600000 # Monthly GET, SELECT, and all other requests (Tier 2).
      monthly_lifecycle_transition_requests: 600000 # Monthly Lifecycle Transition requests.
      monthly_standard_data_retrieval_requests: 600000 # Monthly data Retrieval requests (for standard level of S3 Glacier).
      monthly_bulk_data_retrieval_requests: 600000 # Monthly data Retrieval requests (for bulk level of S3 Glacier).
      monthly_standard_data_retrieval_gb: 6000 # Monthly data retrievals in GB (for standard level of S3 Glacier).
      monthly_bulk_data_retrieval_gb: 6000 # Monthly data retrievals in GB (for bulk level of S3 Glacier).
      early_delete_gb: 600000 # If an archive is deleted within 6 months of being uploaded, you will be charged an early deletion fee per GB.

  aws_secretsmanager_secret:
    monthly_requests: 1000000 # Monthly API requests to Secrets Manager.

  aws_sns_topic:
    monthly_requests: 1000000 # Monthly requests to SNS.
    request_size_kb: 64 # Size of requests to SNS
    http_subscriptions: 100 # Number of HTTP/HTTPS subscriptions
    email_subscriptions: 100 # Number of Email/Email-JSON subscriptions
    kinesis_subscriptions: 0 # Number of Kinesis Firehose subscriptions
    mobile_push_subscriptions: 0 # Number of Mobile Push subscriptions
    macos_subscriptions: 0 # Number of MacOS subscriptions
    sms_subscriptions: 1 # Number of SMS subscriptions
    sms_notification_price: 0.008 # Average price for each SMS notification

  aws_sns_topic_subscription:
    monthly_requests: 1000000 # (DEPRECATED use aws_sns_topic.monthly_requests instead) Monthly requests to SNS.
    request_size_kb: 64       # (DEPRECATED use aws_sns_topic.request_size_kb instead) Size of requests to SNS, billed in 64KB chunks. So 1M requests at 128KB uses 2M requests.

  aws_sqs_queue:
    monthly_requests: 1000000 # Monthly requests to SQS.
    request_size_kb: 64       # Size of requests to SQS, billed in 64KB chunks. So 1M requests at 128KB uses 2M requests.

  aws_ssm_parameter:
    api_throughput_limit: standard    # SSM Parameter Throughput limit, can be: standard, advanced, higher.
    monthly_api_interactions: 1000000 # Monthly API interactions.
    parameter_storage_hrs: 730        # Number of hours in the month parameters will be stored for.

  aws_ssm_activation:
    instance_tier: standard # Instance tier being used, can be: standard, advanced.
    instances: 100          # Number of instances being managed.

  aws_transfer_server:
    monthly_data_downloaded_gb: 50 # Monthly data downloaded over enabled protocols in GB.
    monthly_data_uploaded_gb: 10 # Monthly data uploaded over enabled protocols in GB.

  aws_vpc_endpoint:
    monthly_data_processed_gb: 1000 # Monthly data processed by the VPC endpoint(s) in GB.

  aws_vpn_connection:
    monthly_data_processed_gb: 100 # Monthly data processed through a transit gateway attached to your VPN Connection in GB.

  aws_cloudfront_distribution:
    monthly_data_transfer_to_internet_gb: # Monthly regional data transfer out to internet from the following, in GB:
      us: 51200000          # United States, Mexico, Canada
      europe: 220000        # Europe, Israel
      south_africa: 10000   # South Africa, Kenya, Middle East
      south_america: 50000  # South America
      japan: 387000         # Japan
      australia: 500000     # Australia, New Zealand
      asia_pacific: 1200000 # Hong Kong, Philippines, Singapore, South Korea, Taiwan, Thailand
      india: 200000         # India
    monthly_data_transfer_to_origin_gb: # Monthly regional data transfer out to origin from the following, in GB:
      us: 2200           # United States, Mexico, Canada
      europe: 1000       # Europe, Israel
      south_africa: 300  # South Africa, Kenya, Middle East
      south_america: 200 # South America
      japan: 10          # Japan
      australia: 100     # Australia, New Zealand
      asia_pacific: 30   # Hong Kong, Philippines, Singapore, South Korea, Taiwan, Thailand
      india: 80          # India
    monthly_http_requests: # Monthly number of HTTP requests to:
      us: 80000            # United States, Mexico, Canada
      europe: 40000        # Europe, Israel
      south_africa: 20000  # South Africa, Kenya, Middle East
      south_america: 10000 # South America
      japan: 3000          # Japan
      australia: 15000     # Australia, New Zealand
      asia_pacific: 45000  # Hong Kong, Philippines, Singapore, South Korea, Taiwan, Thailand
      india: 10000         # India
    monthly_https_requests: # Monthly number of HTTPS requests to:
      us: 180000           # United States, Mexico, Canada
      europe: 10000        # Europe, Israel
      south_africa: 50000  # South Africa, Kenya, Middle East
      south_america: 30000 # South America
      japan: 1000          # Japan
      australia: 45000     # Australia, New Zealand
      asia_pacific: 25000  # Hong Kong, Philippines, Singapore, South Korea, Taiwan, Thailand
      india: 30000         # India
    monthly_shield_requests: # Monthly number of shield requests to:
      us: 90000          # United States
      europe: 30000      # Europe
      south_america: 200 # South America
      japan: 12300       # Japan
      australia: 2300    # Australia
      singapore: 58600   # Singapore
      south_korea: 24000 # South Korea
      india: 10000       # India
    monthly_invalidation_requests: 1200 # Monthly number of invalidation requests.
    monthly_encryption_requests: 100000 # Monthly number of field level encryption requests.
    monthly_log_lines: 5000000          # Monthly number of real-time log lines.
    custom_ssl_certificates: 3          # Number of dedicated IP custom SSL certificates.

  aws_sfn_state_machine:
    monthly_transitions: 1000 # Monthly number of state transitions. Only applicable for Standard Workflows.
    monthly_requests: 10000   # Monthly number of workflow requests. Only applicable for Express Workflows.
    memory_mb: 128            # Average amount of memory consumed by workflow in MB. Only applicable for Express Workflows.
    workflow_duration_ms: 500 # Average duration of workflow in milliseconds. Only applicable for Express Workflows.

  aws_waf_web_acl:
    rule_group_rules: 5 # Total number of Rule Group rules used by the Web ACL.
    monthly_requests: 1000000 # Monthly number of web requests received.

  aws_wafv2_web_acl:
    rule_group_rules: 5          # Total number of Rule Group rules used by the Web ACL.
    managed_rule_group_rules: 10 # Total number of Managed Rule Group rules used by the Web ACL.
    monthly_requests: 1000000 # Monthly number of web requests received.

# Use the block below to specify usage for specific resources. An example lambda function is shown.
# resource_usage:
#   aws_lambda_function.my_function:
#     monthly_requests: 100000 # Monthly requests to the Lambda function.
#     request_duration_ms: 500 # Average duration of each request in milliseconds.