# SNS Wrapper basic example

## Introduction
This is an example of the basic SNS wrapper module usage. This one deploys simple, default SNS topic.

## Pre-requisites
Before deploying this module you will need to have the following:

| | |
|------|---------|
|Terraform tool|Used to do the deployment based on the IaC code|
|AWS account|Needed to host the resources you are deploying|
|IAM user and role for deployment|Needed for authenticating with your AWS account and for deploying the resources there|
|:warning: Deployment permissions|The minimum required IAM permissions (least-privilege) for deploying this module can be found in the [iam](iam/) folder under `deployment_policy.json`. Add these permissions to your deployment role policy before deploying!|

## Usage

The easiest way to leverage this example is to copy the contents of this folder to your application repository and perform the steps below.

#### (1) Update Application specific information
- Navigate to [backend.dev.conf](backend.dev.conf) (or [backend.dev-cn.conf](backend.dev-cn.conf) if you want to deploy to `aws-cn`) and update the values with your S3 backend configuration. ( [How to create the s3 backend can be found here.](https://github.com/merck-gen/iac-shared-cf-pattern-aws-remote-state-for-terraform))
- Navigate to [terraform.dev.tfvars](terraform.dev.tfvars) (or [terraform.dev-cn.tfvars](terraform.dev-cn.tfvars) if you want to deploy to `aws-cn`) and update the values for Tags, Common, and any AWS service specific values
- Review `main.tf` and update any **source** values to the latest released version of the module found in [IAC Shared Library releases](https://go.merck.com/iacreleases)

#### (2) Deployment
Run the following commands:
```
terraform init --backend-config backend.dev.conf
terraform plan --var-file terraform.dev.tfvars -out tfplan
terraform apply tfplan
```

Note that this example may create resources which can cost money (AWS Elastic IP, for example). Run `terraform destroy --var-file terraform.dev.tfvars` when you don't need these resources.

<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | ~> 5.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | ~> 5.0 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_aws_kms_key"></a> [aws\_kms\_key](#module\_aws\_kms\_key) | https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-kms-wrapper_2.1.0.tgz | n/a |
| <a name="module_labels"></a> [labels](#module\_labels) | https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-labels-int_3.0.0.tgz | n/a |
| <a name="module_sns_other_topic"></a> [sns\_other\_topic](#module\_sns\_other\_topic) | ../../ | n/a |
| <a name="module_sns_topic"></a> [sns\_topic](#module\_sns\_topic) | ../../ | n/a |

## Resources

| Name | Type |
|------|------|
| [aws_iam_policy_document.data_protection](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |
| [aws_iam_policy_document.sns_topic_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |
| [aws_partition.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/partition) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_account_no"></a> [account\_no](#input\_account\_no) | AWS account number to deploy to | `string` | n/a | yes |
| <a name="input_default_tags"></a> [default\_tags](#input\_default\_tags) | A map of default tags to be applied on each resource. You can get required tags [here](https://cloud.merck.com/documentation/compliance/tagging-standards/index.html) | `map(string)` | n/a | yes |
| <a name="input_deployment_role"></a> [deployment\_role](#input\_deployment\_role) | Terraform Deployment Role | `string` | n/a | yes |
| <a name="input_other_subscriptions"></a> [other\_subscriptions](#input\_other\_subscriptions) | A map of subscription definitions to create | `any` | `{}` | no |
| <a name="input_partition"></a> [partition](#input\_partition) | Second part of aws arn. Used only in the provider configuration. | `string` | `"aws"` | no |
| <a name="input_region"></a> [region](#input\_region) | AWS Region | `string` | `"us-east-1"` | no |
| <a name="input_resource_vars"></a> [resource\_vars](#input\_resource\_vars) | map of tags to pass to labels module | <pre>object({<br/>    appname     = string<br/>    region      = string<br/>    environment = string<br/>    attributes  = list(string)<br/>    label_order = list(string)<br/>  })</pre> | n/a | yes |
| <a name="input_subscriptions"></a> [subscriptions](#input\_subscriptions) | A map of subscription definitions to create | `any` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_sns_other_topic_output"></a> [sns\_other\_topic\_output](#output\_sns\_other\_topic\_output) | Map of outputs of a wrapper. |
| <a name="output_sns_topic_output"></a> [sns\_topic\_output](#output\_sns\_topic\_output) | Map of outputs of a wrapper. |
<!-- END_TF_DOCS -->
