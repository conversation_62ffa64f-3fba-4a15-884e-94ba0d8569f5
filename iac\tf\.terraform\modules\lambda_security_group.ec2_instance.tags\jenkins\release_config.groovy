package jenkins

def get_next_version(String version){
  def versionParts = version.tokenize('.')
  println versionParts
  Integer major = versionParts[0].toInteger()
  Integer minor = versionParts[1].toInteger()
  Integer patch = versionParts[2].toInteger()
  String next_version = major + "." + minor + "." + (patch + 1)
  return next_version
}

pipeline {
  agent { label 'docker' && 'linux' }
  options {
    disableConcurrentBuilds()
    buildDiscarder(logRotator(numToKeepStr: '50', artifactNumToKeepStr: '50'))
    timeout(time: 1, unit: 'HOURS')
  }
  stages {
    stage('Confirm Release') {
      steps {
        script {
          env.RELEASE_COMMIT_ID = confirmRelease()
          gitCommit = env.RELEASE_COMMIT_ID ?: error("RELEASE_COMMIT_ID is not set. Have you confirmed release commit ID?")
          checkoutScm(gitCommitId: gitCommit, gitWipeWorkspace: true)
        }
      }
    }
    stage("Approval") {
      steps{
        script{
          timeout(time: 1, unit: 'HOURS') {
            def approve = input(message: 'Proceed with Release', submitter: "iac-shared-admins")
          }
        }
      }
    }
    stage('Start release') {
      environment {
        VERSION_TAG = readFile "${WORKSPACE}/VERSION" as String
        NEXT_VERSION = get_next_version(env.VERSION_TAG.trim()).trim()
      }
      stages {
        stage("Tag Git Repo") {
          steps {
            script {
              withCredentials([usernamePassword(credentialsId: 'srviacpipeline-pat', passwordVariable: 'SRVPASS', usernameVariable: 'SRVUSER')]) {
                sh "TOKEN=${SRVPASS} ./scripts/release_helper.sh tag_repo ${VERSION_TAG}"
              }
            }
          }
        }
        stage('Upload released module to Artifactory') {
          when {
            branch 'main'
          }
          agent {
            docker {
              image 'base-image.dock.merck.com/dostack/jfrog-cli-v2/2.9.0/rel-2021w51'
              args '-u 0:0 --entrypoint ""'
              reuseNode true
            }
          }
          steps {
            // TODO: Add the user below a write permission to your Bitbucket repository
            withCredentials([usernamePassword(credentialsId: 'srviacpipeline', passwordVariable: 'SRVPASS', usernameVariable: 'SRVUSER')]) {
              withGitHttpAuth(credentialsId: 'srviacpipeline') {
                sh "VERSION=${env.VERSION_TAG.trim()} ./scripts/upload.sh"
              }
            }
          }
        }
        stage("Revert the source change commit") {
          steps {
            script {
              withCredentials([usernamePassword(credentialsId: 'srviacpipeline-pat', passwordVariable: 'SRVPASS', usernameVariable: 'SRVUSER')]) {
                sh "TOKEN=${SRVPASS} ./scripts/release_helper.sh revert_release_commit ${VERSION_TAG}"
              }
            }
          }
        }
        stage("Set Next Version") {
          steps {
            script {
              withCredentials([usernamePassword(credentialsId: 'srviacpipeline-pat', passwordVariable: 'SRVPASS', usernameVariable: 'SRVUSER')]) {
                writeFile file: "${WORKSPACE}/VERSION" as String, text: NEXT_VERSION as String
                sh "TOKEN=${SRVPASS} ./scripts/release_helper.sh set_next_version ${NEXT_VERSION}"
              }
            }
          }
        }
      }
    }
    stage('Trigger Confluence Release page update') {
      steps {
        build 'GITHUB/iac-shared/iac-shared-parent/iac-upload-to-confluence'
      }
    }
  }
}
