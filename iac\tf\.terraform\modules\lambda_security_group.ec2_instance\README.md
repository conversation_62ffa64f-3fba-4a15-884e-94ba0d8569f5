# EC2 Instance Wrapper Module

[![CI Pipeline](https://github.com/merck-gen/iac-shared-tf-module-aws-ec2-instance-wrapper/actions/workflows/branch-ci.yaml/badge.svg)](https://github.com/merck-gen/iac-shared-tf-module-aws-ec2-instance-wrapper/actions/workflows/branch-ci.yaml) [![Terratest](https://github.com/merck-gen/iac-shared-tf-module-aws-ec2-instance-wrapper/actions/workflows/terratest.yaml/badge.svg)](https://github.com/merck-gen/iac-shared-tf-module-aws-ec2-instance-wrapper/actions/workflows/terratest.yaml) 

## Introduction

This is the Merck wrapper for [EC2 Instance community modules](https://github.com/merck-gen/iac-shared-tf-module-aws-ec2-instance-ext). The purpose of this module is to help you deploy a compliant [EC2 instance](https://cloud.merck.com/documentation/native-services/aws/ec2/index.html) in your AWS account.

## Pre-requisites

Before deploying this module you will need to have the following:

| | |
|------|---------|
|Terraform tool|Used to do the deployment based on the IaC code|
|AWS account|Needed to host the resources you are deploying|
|IAM user and role for deployment|Needed for authenticating with your AWS account and for deploying the resources there|
|Deployment permissions|Check the [AWS Native Service](https://cloud.merck.com/documentation/native-services/aws/ec2/index.html) page for required SCP exception tags for the deployment role|

| :warning:  Note!        |
|:---------------------------|
| Please check what is the latest available release version of this module: [IAC Shared library releases](https://go.merck.com/iacreleases)     |

## Usage

|  |  |
|------|---------|
|Examples|Please check the [examples](examples/) folder to see how this module is used.|
|Configurations|Anything listed in the [Inputs section of this readme file](#inputs) can be configured by the users. The module has some hardcoded values based on the [company security requirements](https://cloud.merck.com/documentation/native-services/aws/ec2/) which the user can't change. You can find the hardcoded values in [main.tf](main.tf).|
|Cost optimization|Please visit [Cloud Services documentation](https://cloud.merck.com/documentation/finance/cost-optimization/index.html) to learn how to optimize EC2, EBS infrastructure and other AWS services.|

### Waiting for instance user data to finish

To wait for instance initialization, you can use the `wait_for_instance` variable. You can use the default wait script or create your own if you want.

> [!NOTE]  
> Default wait script require Bash and aws-cli to be present as we are using `local-exec` provider.

You need to adjust your user data script in a way that allows you to tag instance appropriatelly (fail/success). Please check the [user-data.sh.tpl](examples/complete/user-data.sh.tpl) and [main.tf](examples/complete/main.tf) in the [complete](examples/complete/) example.

## Contributing

Everyone is welcome to contribute to the code of this module! Please see the [instructions on how to contribute on IAC Shared home page](https://go.merck.com/iac).

## Contact

In case any issues regarding the module please comment on the [MS Teams Infrastructure as Code channel](https://teams.microsoft.com/l/channel/19%3acc5325594d1543069ea7270bcd393da1%40thread.skype/Infrastructure%2520as%2520Code?groupId=7d021e32-5295-41c2-ad7c-24f0f6de8ff4&tenantId=a00de4ec-48a8-43a6-be74-e31274e2060d) or connect via email <<EMAIL>>.

## Linux Team Merck Customization

Link for [Instructions for use of Image Bakery Linux Image ](https://share.merck.com/display/CIVT/Instructions+for+use+of+Image+Bakery+Linux+Image)

<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.2.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | >= 5.0.1 |
| <a name="requirement_local"></a> [local](#requirement\_local) | >= 2.4.0 |
| <a name="requirement_null"></a> [null](#requirement\_null) | >= 3.2.2 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | >= 5.0.1 |
| <a name="provider_local"></a> [local](#provider\_local) | >= 2.4.0 |
| <a name="provider_null"></a> [null](#provider\_null) | >= 3.2.2 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_ec2_instance_wrapper"></a> [ec2\_instance\_wrapper](#module\_ec2\_instance\_wrapper) | artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/ec2-instance/aws | 5.7.1 |
| <a name="module_tags"></a> [tags](#module\_tags) | https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz | n/a |

## Resources

| Name | Type |
|------|------|
| [local_file.wait_for_instance](https://registry.terraform.io/providers/hashicorp/local/latest/docs/resources/file) | resource |
| [null_resource.wait_for_instance](https://registry.terraform.io/providers/hashicorp/null/latest/docs/resources/resource) | resource |
| [aws_region.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/region) | data source |
| [local_file.version](https://registry.terraform.io/providers/hashicorp/local/latest/docs/data-sources/file) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_ami"></a> [ami](#input\_ami) | ID of AMI to use for the instance | `string` | `null` | no |
| <a name="input_ami_ssm_parameter"></a> [ami\_ssm\_parameter](#input\_ami\_ssm\_parameter) | SSM parameter name for the AMI ID. For Amazon Linux AMI SSM parameters see [reference](https://docs.aws.amazon.com/systems-manager/latest/userguide/parameter-store-public-parameters-ami.html) | `string` | `"/aws/service/ami-amazon-linux-latest/amzn2-ami-hvm-x86_64-gp2"` | no |
| <a name="input_availability_zone"></a> [availability\_zone](#input\_availability\_zone) | AZ to start the instance in | `string` | `null` | no |
| <a name="input_capacity_reservation_specification"></a> [capacity\_reservation\_specification](#input\_capacity\_reservation\_specification) | Describes an instance's Capacity Reservation targeting option | `any` | `{}` | no |
| <a name="input_cpu_core_count"></a> [cpu\_core\_count](#input\_cpu\_core\_count) | Sets the number of CPU cores for an instance | `number` | `null` | no |
| <a name="input_cpu_credits"></a> [cpu\_credits](#input\_cpu\_credits) | The credit option for CPU usage (unlimited or standard) | `string` | `null` | no |
| <a name="input_cpu_options"></a> [cpu\_options](#input\_cpu\_options) | Defines CPU options to apply to the instance at launch time. | `any` | `{}` | no |
| <a name="input_cpu_threads_per_core"></a> [cpu\_threads\_per\_core](#input\_cpu\_threads\_per\_core) | Sets the number of CPU threads per core for an instance (has no effect unless cpu\_core\_count is also set) | `number` | `null` | no |
| <a name="input_create"></a> [create](#input\_create) | Whether to create an instance | `bool` | `true` | no |
| <a name="input_create_eip"></a> [create\_eip](#input\_create\_eip) | Determines whether a public EIP will be created and associated with the instance. | `bool` | `false` | no |
| <a name="input_create_iam_instance_profile"></a> [create\_iam\_instance\_profile](#input\_create\_iam\_instance\_profile) | Determines whether an IAM instance profile is created or to use an existing IAM instance profile | `bool` | `true` | no |
| <a name="input_create_spot_instance"></a> [create\_spot\_instance](#input\_create\_spot\_instance) | Depicts if the instance is a spot instance | `bool` | `false` | no |
| <a name="input_default_tags"></a> [default\_tags](#input\_default\_tags) | A map of default tags to be applied on each resource. You can get required tags [here](https://cloud.merck.com/documentation/compliance/tagging-standards/index.html) | `map(string)` | n/a | yes |
| <a name="input_disable_api_stop"></a> [disable\_api\_stop](#input\_disable\_api\_stop) | If true, enables EC2 Instance Stop Protection | `bool` | `null` | no |
| <a name="input_disable_api_termination"></a> [disable\_api\_termination](#input\_disable\_api\_termination) | If true, enables EC2 Instance Termination Protection | `bool` | `null` | no |
| <a name="input_ebs_optimized"></a> [ebs\_optimized](#input\_ebs\_optimized) | If true, the launched EC2 instance will be EBS-optimized | `bool` | `true` | no |
| <a name="input_ebs_volumes"></a> [ebs\_volumes](#input\_ebs\_volumes) | Configuration parameters for EBS block device/s<pre>list(object({<br/>  device_name           = string, # Name of EBS device<br/>  volume_size           = optional(number),     # Size of additional EBS block devices to attach to the instance<br/>  volume_type           = optional(string),     # Type of additional EBS block devices to attach to the instance<br/>  kms_key_id            = optional(string),     # EBS block devices to attach to the instance<br/>  throughput            = optional(number),     # Additional EBS block devices to attach to the instance<br/>  delete_on_termination = optional(bool),       # Whether the volume should be destroyed on instance termination. Defaults to true.<br/>  iops                  = optional(number),     # Amount of provisioned IOPS. Only valid for volume_type of io1, io2 or gp3.<br/>  snapshot_id           = optional(string),     # Snapshot ID to mount.<br/>  volume_size           = optional(string),     # Size of the volume in gibibytes (GiB).<br/>  volume_type           = optional(string),     # Type of volume. Valid values include standard, gp2, gp3, io1, io2, sc1, or st1. Defaults to gp2.<br/>  tags                  = optional(map(string)) # Map of tags to assign to the device.<br/>}))</pre> | <pre>list(object({<br/>    device_name           = string<br/>    volume_size           = optional(string)<br/>    volume_type           = optional(string)<br/>    kms_key_id            = optional(string)<br/>    throughput            = optional(number)<br/>    delete_on_termination = optional(bool)<br/>    iops                  = optional(number)<br/>    snapshot_id           = optional(string)<br/>    tags                  = optional(map(string))<br/>  }))</pre> | <pre>[<br/>  {<br/>    "device_name": "/dev/sdf",<br/>    "kms_key_id": null,<br/>    "throughput": 125,<br/>    "volume_size": 5,<br/>    "volume_type": "gp3"<br/>  }<br/>]</pre> | no |
| <a name="input_eip_domain"></a> [eip\_domain](#input\_eip\_domain) | Indicates if this EIP is for use in VPC | `string` | `"vpc"` | no |
| <a name="input_eip_tags"></a> [eip\_tags](#input\_eip\_tags) | A map of additional tags to add to the eip | `map(string)` | `{}` | no |
| <a name="input_enable_coreinfra_backup"></a> [enable\_coreinfra\_backup](#input\_enable\_coreinfra\_backup) | Whether to enable default CoreInfra-backup | `bool` | `true` | no |
| <a name="input_enable_coreinfra_backup_copy_to_bunker"></a> [enable\_coreinfra\_backup\_copy\_to\_bunker](#input\_enable\_coreinfra\_backup\_copy\_to\_bunker) | Whether to enable default CoreInfra-backup-copy-to-bunker | `bool` | `false` | no |
| <a name="input_enable_ebs_creation"></a> [enable\_ebs\_creation](#input\_enable\_ebs\_creation) | Whether to create an EBS block device | `bool` | `true` | no |
| <a name="input_enable_volume_tags"></a> [enable\_volume\_tags](#input\_enable\_volume\_tags) | Whether to enable volume tags (if enabled it conflicts with root\_block\_device tags) | `bool` | `true` | no |
| <a name="input_enclave_options_enabled"></a> [enclave\_options\_enabled](#input\_enclave\_options\_enabled) | Whether Nitro Enclaves will be enabled on the instance. Defaults to `false` | `bool` | `null` | no |
| <a name="input_ephemeral_block_device"></a> [ephemeral\_block\_device](#input\_ephemeral\_block\_device) | Customize Ephemeral (also known as Instance Store) volumes on the instance | `list(map(string))` | `[]` | no |
| <a name="input_get_password_data"></a> [get\_password\_data](#input\_get\_password\_data) | If true, wait for password data to become available and retrieve it | `bool` | `null` | no |
| <a name="input_hibernation"></a> [hibernation](#input\_hibernation) | If true, the launched EC2 instance will support hibernation | `bool` | `null` | no |
| <a name="input_host_id"></a> [host\_id](#input\_host\_id) | ID of a dedicated host that the instance will be assigned to. Use when an instance is to be launched on a specific dedicated host | `string` | `null` | no |
| <a name="input_http_endpoint"></a> [http\_endpoint](#input\_http\_endpoint) | Customize the metadata options of the instance | `string` | `"enabled"` | no |
| <a name="input_iam_instance_profile"></a> [iam\_instance\_profile](#input\_iam\_instance\_profile) | IAM Instance Profile to launch the instance with. Specified as the name of the Instance Profile | `string` | n/a | yes |
| <a name="input_iam_role_description"></a> [iam\_role\_description](#input\_iam\_role\_description) | Description of the role | `string` | `null` | no |
| <a name="input_iam_role_name"></a> [iam\_role\_name](#input\_iam\_role\_name) | Name to use on IAM role created | `string` | `null` | no |
| <a name="input_iam_role_path"></a> [iam\_role\_path](#input\_iam\_role\_path) | IAM role path | `string` | `null` | no |
| <a name="input_iam_role_permissions_boundary"></a> [iam\_role\_permissions\_boundary](#input\_iam\_role\_permissions\_boundary) | ARN of the policy that is used to set the permissions boundary for the IAM role | `string` | `null` | no |
| <a name="input_iam_role_policies"></a> [iam\_role\_policies](#input\_iam\_role\_policies) | Policies attached to the IAM role | `map(string)` | `{}` | no |
| <a name="input_iam_role_tags"></a> [iam\_role\_tags](#input\_iam\_role\_tags) | A map of additional tags to add to the IAM role/profile created | `map(string)` | `{}` | no |
| <a name="input_iam_role_use_name_prefix"></a> [iam\_role\_use\_name\_prefix](#input\_iam\_role\_use\_name\_prefix) | Determines whether the IAM role name (`iam_role_name` or `name`) is used as a prefix | `bool` | `true` | no |
| <a name="input_ignore_ami_changes"></a> [ignore\_ami\_changes](#input\_ignore\_ami\_changes) | Whether changes to the AMI ID changes should be ignored by Terraform. Note - changing this value will result in the replacement of the instance | `bool` | `false` | no |
| <a name="input_instance_initiated_shutdown_behavior"></a> [instance\_initiated\_shutdown\_behavior](#input\_instance\_initiated\_shutdown\_behavior) | Shutdown behavior for the instance. Amazon defaults this to stop for EBS-backed instances and terminate for instance-store instances. Cannot be set on instance-store instance | `string` | `null` | no |
| <a name="input_instance_tags"></a> [instance\_tags](#input\_instance\_tags) | Additional tags for the instance | `map(string)` | `{}` | no |
| <a name="input_instance_type"></a> [instance\_type](#input\_instance\_type) | The type of instance to start | `string` | `"t3a.micro"` | no |
| <a name="input_ipv6_address_count"></a> [ipv6\_address\_count](#input\_ipv6\_address\_count) | A number of IPv6 addresses to associate with the primary network interface. Amazon EC2 chooses the IPv6 addresses from the range of your subnet | `number` | `null` | no |
| <a name="input_ipv6_addresses"></a> [ipv6\_addresses](#input\_ipv6\_addresses) | Specify one or more IPv6 addresses from the range of the subnet to associate with the primary network interface | `list(string)` | `null` | no |
| <a name="input_key_name"></a> [key\_name](#input\_key\_name) | Key name of the Key Pair to use for the instance; which can be managed using the `aws_key_pair` resource | `string` | `null` | no |
| <a name="input_launch_template"></a> [launch\_template](#input\_launch\_template) | Specifies a Launch Template to configure the instance. Parameters configured on this resource will override the corresponding parameters in the Launch Template | `map(string)` | `{}` | no |
| <a name="input_maintenance_options"></a> [maintenance\_options](#input\_maintenance\_options) | The maintenance options for the instance | `any` | `{}` | no |
| <a name="input_name"></a> [name](#input\_name) | Name to be used on EC2 instance created | `string` | `""` | no |
| <a name="input_network_interface"></a> [network\_interface](#input\_network\_interface) | Customize network interfaces to be attached at instance boot time | `list(map(string))` | `[]` | no |
| <a name="input_placement_group"></a> [placement\_group](#input\_placement\_group) | The Placement Group to start the instance in | `string` | `null` | no |
| <a name="input_private_dns_name_options"></a> [private\_dns\_name\_options](#input\_private\_dns\_name\_options) | Customize the private DNS name options of the instance | `map(string)` | `{}` | no |
| <a name="input_private_ip"></a> [private\_ip](#input\_private\_ip) | Private IP address to associate with the instance in a VPC | `string` | `null` | no |
| <a name="input_root_block_device_deletion_on_termination"></a> [root\_block\_device\_deletion\_on\_termination](#input\_root\_block\_device\_deletion\_on\_termination) | Whether the volume should be destroyed on instance termination. Defaults to true. | `bool` | `true` | no |
| <a name="input_root_block_device_iops"></a> [root\_block\_device\_iops](#input\_root\_block\_device\_iops) | Amount of provisioned IOPS. Only valid for volume\_type of io1, io2 or gp3.. | `number` | `null` | no |
| <a name="input_root_block_device_kms"></a> [root\_block\_device\_kms](#input\_root\_block\_device\_kms) | Amazon Resource Name (ARN) of the KMS Key to use when encrypting the volume. Must be configured to perform drift detection. | `string` | `null` | no |
| <a name="input_root_block_device_throughput"></a> [root\_block\_device\_throughput](#input\_root\_block\_device\_throughput) | Customize details about the root block device of the instance. | `string` | `200` | no |
| <a name="input_root_block_device_volume_size"></a> [root\_block\_device\_volume\_size](#input\_root\_block\_device\_volume\_size) | Customize details about the root block device of the instance. | `string` | `50` | no |
| <a name="input_root_block_device_volume_type"></a> [root\_block\_device\_volume\_type](#input\_root\_block\_device\_volume\_type) | Customize details about the root block device of the instance. | `string` | `"gp3"` | no |
| <a name="input_root_block_tags"></a> [root\_block\_tags](#input\_root\_block\_tags) | A mapping of tags to assign to the resource | `map(string)` | `{}` | no |
| <a name="input_secondary_private_ips"></a> [secondary\_private\_ips](#input\_secondary\_private\_ips) | A list of secondary private IPv4 addresses to assign to the instance's primary network interface (eth0) in a VPC. Can only be assigned to the primary network interface (eth0) attached at instance creation, not a pre-existing network interface i.e. referenced in a `network_interface block` | `list(string)` | `null` | no |
| <a name="input_source_dest_check"></a> [source\_dest\_check](#input\_source\_dest\_check) | Controls if traffic is routed to the instance when the destination address does not match the instance. Used for NAT or VPNs | `bool` | `true` | no |
| <a name="input_spot_block_duration_minutes"></a> [spot\_block\_duration\_minutes](#input\_spot\_block\_duration\_minutes) | The required duration for the Spot instances, in minutes. This value must be a multiple of 60 (60, 120, 180, 240, 300, or 360) | `number` | `null` | no |
| <a name="input_spot_instance_interruption_behavior"></a> [spot\_instance\_interruption\_behavior](#input\_spot\_instance\_interruption\_behavior) | Indicates Spot instance behavior when it is interrupted. Valid values are `terminate`, `stop`, or `hibernate` | `string` | `null` | no |
| <a name="input_spot_launch_group"></a> [spot\_launch\_group](#input\_spot\_launch\_group) | A launch group is a group of spot instances that launch together and terminate together. If left empty instances are launched and terminated individually | `string` | `null` | no |
| <a name="input_spot_price"></a> [spot\_price](#input\_spot\_price) | The maximum price to request on the spot market. Defaults to on-demand price | `string` | `null` | no |
| <a name="input_spot_type"></a> [spot\_type](#input\_spot\_type) | If set to one-time, after the instance is terminated, the spot request will be closed. Default `persistent` | `string` | `null` | no |
| <a name="input_spot_valid_from"></a> [spot\_valid\_from](#input\_spot\_valid\_from) | The start date and time of the request, in UTC RFC3339 format(for example, YYYY-MM-DDTHH:MM:SSZ) | `string` | `null` | no |
| <a name="input_spot_valid_until"></a> [spot\_valid\_until](#input\_spot\_valid\_until) | The end date and time of the request, in UTC RFC3339 format(for example, YYYY-MM-DDTHH:MM:SSZ) | `string` | `null` | no |
| <a name="input_spot_wait_for_fulfillment"></a> [spot\_wait\_for\_fulfillment](#input\_spot\_wait\_for\_fulfillment) | If set, Terraform will wait for the Spot Request to be fulfilled, and will throw an error if the timeout of 10m is reached | `bool` | `null` | no |
| <a name="input_subnet_id"></a> [subnet\_id](#input\_subnet\_id) | The VPC Subnet ID to launch in | `string` | `null` | no |
| <a name="input_tags"></a> [tags](#input\_tags) | A mapping of tags to assign to the resource | `map(string)` | `{}` | no |
| <a name="input_tenancy"></a> [tenancy](#input\_tenancy) | The tenancy of the instance (if the instance is running in a VPC). Available values: default, dedicated, host | `string` | `null` | no |
| <a name="input_timeouts"></a> [timeouts](#input\_timeouts) | Define maximum timeout for creating, updating, and deleting EC2 instance resources | `map(string)` | `{}` | no |
| <a name="input_user_data"></a> [user\_data](#input\_user\_data) | The user data to provide when launching the instance. Do not pass gzip-compressed data via this argument; see user\_data\_base64 instead | `string` | `null` | no |
| <a name="input_user_data_base64"></a> [user\_data\_base64](#input\_user\_data\_base64) | Can be used instead of user\_data to pass base64-encoded binary data directly. Use this instead of user\_data whenever the value is not a valid UTF-8 string. For example, gzip-encoded user data must be base64-encoded and passed via this argument to avoid corruption | `string` | `null` | no |
| <a name="input_user_data_replace_on_change"></a> [user\_data\_replace\_on\_change](#input\_user\_data\_replace\_on\_change) | When used in combination with user\_data or user\_data\_base64 will trigger a destroy and recreate when set to true. Defaults to false if not set | `bool` | `false` | no |
| <a name="input_volume_tags"></a> [volume\_tags](#input\_volume\_tags) | A mapping of tags to assign to the devices created by the instance at launch time | `map(string)` | `{}` | no |
| <a name="input_vpc_security_group_ids"></a> [vpc\_security\_group\_ids](#input\_vpc\_security\_group\_ids) | A list of security group IDs to associate with | `list(string)` | `null` | no |
| <a name="input_wait_for_instance"></a> [wait\_for\_instance](#input\_wait\_for\_instance) | Use this variable to configure [wait.sh.tpl](wait.sh.tpl) script. If you want to use custom script, please know that<br/>INSTANCE\_ID, SLEEP\_TIME, NUM\_OF\_CYCLES, FAILED\_VALUE, TAG\_NAME and DEPLOYMENT\_ROLE\_ARN are template variables and are going to be replaced in your script.<pre>terraform<br/>type = object({<br/>  enabled             (Optional) whether the null_resource for waiting should be created<br/>  custom_script       (Optional) path to template file which will be used to wait for ec2 instance to finish the user data, if not specified, bash script in this module is going to be used<br/>  sleep_time          (Optional) for how long should we sleep between checking for tag<br/>  num_of_cycles       (Optional) how many cycles of wait are going to happen before we give up<br/>  region              (Optional) region where the ec2 instance is deployed to, if nothing is passed, output from data source is going to be used<br/>  interpreter         (Optional) which interpreter is going to be used, defaults to bash<br/>  failed_value        value which we set as a failure<br/>  tag_name            name of a tag for which we will wait<br/>  deployment_role_arn role that has ec2:DescribeTags permission<br/>})</pre> | <pre>object({<br/>    enabled       = optional(bool, false)<br/>    custom_script = optional(string)<br/>    sleep_time    = optional(number, 10)<br/>    num_of_cycles = optional(number, 1000)<br/>    region        = optional(string)<br/>    interpreter = optional(list(string), [<br/>      "bash",<br/>      "-c"<br/>    ])<br/>    failed_value        = string<br/>    tag_name            = string<br/>    deployment_role_arn = string<br/>  })</pre> | <pre>{<br/>  "deployment_role_arn": "",<br/>  "enabled": false,<br/>  "failed_value": "failed",<br/>  "tag_name": "initialization"<br/>}</pre> | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_ami"></a> [ami](#output\_ami) | AMI ID that was used to create the instance |
| <a name="output_arn"></a> [arn](#output\_arn) | The ARN of the instance |
| <a name="output_availability_zone"></a> [availability\_zone](#output\_availability\_zone) | The availability zone of the created instance |
| <a name="output_capacity_reservation_specification"></a> [capacity\_reservation\_specification](#output\_capacity\_reservation\_specification) | Capacity reservation specification of the instance |
| <a name="output_ebs_block_device"></a> [ebs\_block\_device](#output\_ebs\_block\_device) | EBS block device information |
| <a name="output_ephemeral_block_device"></a> [ephemeral\_block\_device](#output\_ephemeral\_block\_device) | Ephemeral block device information |
| <a name="output_iam_instance_profile_arn"></a> [iam\_instance\_profile\_arn](#output\_iam\_instance\_profile\_arn) | ARN assigned by AWS to the instance profile |
| <a name="output_iam_instance_profile_id"></a> [iam\_instance\_profile\_id](#output\_iam\_instance\_profile\_id) | Instance profile's ID |
| <a name="output_iam_instance_profile_unique"></a> [iam\_instance\_profile\_unique](#output\_iam\_instance\_profile\_unique) | Stable and unique string identifying the IAM instance profile |
| <a name="output_iam_role_arn"></a> [iam\_role\_arn](#output\_iam\_role\_arn) | The Amazon Resource Name (ARN) specifying the IAM role |
| <a name="output_iam_role_name"></a> [iam\_role\_name](#output\_iam\_role\_name) | The name of the IAM role |
| <a name="output_iam_role_unique_id"></a> [iam\_role\_unique\_id](#output\_iam\_role\_unique\_id) | Stable and unique string identifying the IAM role |
| <a name="output_id"></a> [id](#output\_id) | The ID of the instance |
| <a name="output_instance_state"></a> [instance\_state](#output\_instance\_state) | The state of the instance |
| <a name="output_ipv6_addresses"></a> [ipv6\_addresses](#output\_ipv6\_addresses) | The IPv6 address assigned to the instance, if applicable |
| <a name="output_outpost_arn"></a> [outpost\_arn](#output\_outpost\_arn) | The ARN of the Outpost the instance is assigned to |
| <a name="output_password_data"></a> [password\_data](#output\_password\_data) | Base-64 encoded encrypted password data for the instance. Useful for getting the administrator password for instances running Microsoft Windows. This attribute is only exported if `get_password_data` is true |
| <a name="output_primary_network_interface_id"></a> [primary\_network\_interface\_id](#output\_primary\_network\_interface\_id) | The ID of the instance's primary network interface |
| <a name="output_private_dns"></a> [private\_dns](#output\_private\_dns) | The private DNS name assigned to the instance. Can only be used inside the Amazon EC2, and only available if you've enabled DNS hostnames for your VPC |
| <a name="output_private_ip"></a> [private\_ip](#output\_private\_ip) | The private IP address assigned to the instance |
| <a name="output_public_dns"></a> [public\_dns](#output\_public\_dns) | The public DNS name assigned to the instance. For EC2-VPC, this is only available if you've enabled DNS hostnames for your VPC |
| <a name="output_public_ip"></a> [public\_ip](#output\_public\_ip) | The public IP address assigned to the instance, if applicable. |
| <a name="output_root_block_device"></a> [root\_block\_device](#output\_root\_block\_device) | Root block device information |
| <a name="output_spot_bid_status"></a> [spot\_bid\_status](#output\_spot\_bid\_status) | The current bid status of the Spot Instance Request |
| <a name="output_spot_instance_id"></a> [spot\_instance\_id](#output\_spot\_instance\_id) | The Instance ID (if any) that is currently fulfilling the Spot Instance request |
| <a name="output_spot_request_state"></a> [spot\_request\_state](#output\_spot\_request\_state) | The current request state of the Spot Instance Request |
| <a name="output_tags_all"></a> [tags\_all](#output\_tags\_all) | A map of tags assigned to the resource, including those inherited from the provider default\_tags configuration block |
<!-- END_TF_DOCS -->
