fail_fast: true
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.3.0
    hooks:
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: check-vcs-permalinks
      - id: end-of-file-fixer
      - id: trailing-whitespace
        args: [--markdown-linebreak-ext=md]
      - id: check-yaml
      - id: check-case-conflict
      - id: mixed-line-ending
        args: [--fix=lf]

  - repo: local
    hooks:
    - id: gitleaks
      name: GitLeaks
      description: Detect hardcoded secrets using Gitleaks
      entry: --entrypoint sh base-image.dock.merck.com/accd/tool-gitleaks/rel-2022w42.1:latest -c "gitleaks detect -c .gitleaks.toml --source=."
      language: docker_image
    - id: terraform-fmt
      name: terraform-fmt
      description: Terraform validation
      entry: --entrypoint sh iac-shared.dock.merck.com/terraform/1.5.7:rel-2023w44.21 -c "terraform fmt -recursive ."
      language: docker_image
    - id: terraform-validate
      name: terraform-validate
      description: Terraform validation
      entry: --entrypoint sh iac-shared.dock.merck.com/terraform/1.5.7:rel-2023w44.21 -c "terraform init && terraform validate ."
      language: docker_image
    - id: terraform-docs
      name: terraform-docs
      description: Terraform docs
      entry: --entrypoint sh iac-shared.dock.merck.com/terraform/1.5.7:rel-2023w44.21 -c "terraform-docs markdown table --output-file README.md --output-mode inject ."
      language: docker_image
    - id: tfsec
      name: tfsec
      description: Terraform validation
      entry: --entrypoint sh iac-shared.dock.merck.com/terraform/1.5.7:rel-2023w44.21 -c "tfsec ."
      language: docker_image
    - id: tflint
      name: tflint
      description: Terraform validation
      entry: --entrypoint sh -u root iac-shared.dock.merck.com/terraform/1.5.7:rel-2023w44.21 -c "tflint --init && tflint ."
      language: docker_image
    - id: checkov
      name: checkov
      description: Terraform validation
      entry: --entrypoint sh base-image.dock.merck.com/accd/tool-checkov/checkov/2.0.930/rel-2022w43:latest -c "checkov --config-file .checkov.yaml -d ."
      language: docker_image
    - id: adr-log
      name: adr-log
      description: ADRS documentation generation
      entry: --entrypoint sh base-image.dock.merck.com/dostack/adr-log/2.2.0/rel-2021w51 -c "adr-log -i -d docs/ADRS/"
      language: docker_image
