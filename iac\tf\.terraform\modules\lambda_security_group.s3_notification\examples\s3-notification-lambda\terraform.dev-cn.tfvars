######################################################################################
# Required "User Provided" Variables
# you must modify these with application specific information
######################################################################################

# Labels - this will create consistent naming and tagging across all resources
# All labels become part of the AWS resource name based on label_order
resource_vars = {
  appname     = "tft"
  region      = "cn1"
  attributes  = ["wrap", "s3-notification-lambda"]
  environment = "dev"
  label_order = ["appname", "environment"]
}

# Tags - the following tags will be added across all resources
default_tags = {
  DataClassification = "Proprietary"
  Consumer           = "<EMAIL>"
  Application        = "tft"
  Environment        = "Development"
  Service            = "TFT-Development"
}

# AWS Account Information
region          = "cn-north-1"
partition       = "aws-cn"
account_no      = "************"
deployment_role = "deployment-role"
vpc_id          = "vpc-0f666efa794251c3e"
subnet_ids      = ["subnet-0dc57cf167a4181e5", "subnet-0eef172944d35f94a"]

######################################################################################
# Optional "User Provided" Variables
# you can modify the values below to suit your application as needed
######################################################################################

########## S3 Buckets Configuration ############
# Number of buckets requested, an S3 (source) bucket will be created for each name listed below
kms_key_id    = "" # leave blank "" to have a KMS key created for you, use "null" if sse_algorithm is AES256, or enter an existing key arn
sse_algorithm = "" # valid options are AES256 or aws:kms, defaults to aws:kms if blank

s3_bucket_versioning = {
  status     = "Enabled" # enabled is required if requesting a replica bucket
  mfa_delete = "Disabled"
}

create_replica_bucket         = false # a replica bucket will be created for each source bucket requested above
force_destroy                 = true  # For testing purposes only
website                       = {}
s3_bucket_cors_rule           = []
s3_bucket_lifecycle_rule      = []
s3_bucket_intelligent_tiering = []

########## Lambda ############
cloudwatch_logs_retention_in_days = 7
lambda_authorizer_description     = "API Gateway lambda authorizer"
authorizer_handler                = "index.lambda_handler"
authorizer_runtime                = "python3.8"
authorizer_publish                = true
authorizer_create_package         = false
authorizer_local_existing_package = "./lambda_authorizer/existing_package.zip"

