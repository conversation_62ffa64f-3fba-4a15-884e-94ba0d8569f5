locals {
  logging = merge({
    target_bucket = var.logging.create ? module.s3_log_bucket.s3_bucket_id : var.logging.target_bucket
    target_prefix = var.logging.target_prefix
    },
    var.logging.target_object_key_format != null ? {
      target_object_key_format = {
        for i, j in var.logging.target_object_key_format :
        i => j if j != null
      }
    } : {},
  )
}

#>>GENERATE_CONTENT
module "s3_bucket_wrapper" {
  source  = "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/s3-bucket/aws"
  version = "4.2.2"

  acceleration_status                        = var.acceleration_status
  access_log_delivery_policy_source_accounts = var.access_log_delivery_policy_source_accounts
  access_log_delivery_policy_source_buckets  = var.access_log_delivery_policy_source_buckets
  acl                                        = var.acl
  allowed_kms_key_arn                        = var.allowed_kms_key_arn
  analytics_configuration                    = var.analytics_configuration
  analytics_self_source_destination          = var.analytics_self_source_destination
  analytics_source_account_id                = var.analytics_source_account_id
  analytics_source_bucket_arn                = var.analytics_source_bucket_arn
  attach_access_log_delivery_policy          = var.attach_access_log_delivery_policy
  attach_analytics_destination_policy        = var.attach_analytics_destination_policy
  attach_deny_incorrect_encryption_headers   = var.attach_deny_incorrect_encryption_headers
  attach_deny_incorrect_kms_key_sse          = var.attach_deny_incorrect_kms_key_sse
  attach_deny_insecure_transport_policy      = true
  attach_deny_unencrypted_object_uploads     = var.attach_deny_unencrypted_object_uploads
  attach_elb_log_delivery_policy             = var.attach_elb_log_delivery_policy
  attach_inventory_destination_policy        = var.attach_inventory_destination_policy
  attach_lb_log_delivery_policy              = var.attach_lb_log_delivery_policy
  attach_policy                              = var.attach_policy
  attach_public_policy                       = var.attach_public_policy
  attach_require_latest_tls_policy           = true
  block_public_acls                          = true
  block_public_policy                        = true
  bucket                                     = var.bucket
  bucket_prefix                              = var.bucket_prefix
  control_object_ownership                   = var.control_object_ownership
  cors_rule                                  = var.cors_rule
  create_bucket                              = var.create_bucket
  expected_bucket_owner                      = var.expected_bucket_owner
  force_destroy                              = var.force_destroy
  grant                                      = var.grant
  ignore_public_acls                         = true
  intelligent_tiering                        = var.intelligent_tiering
  inventory_configuration                    = var.inventory_configuration
  inventory_self_source_destination          = var.inventory_self_source_destination
  inventory_source_account_id                = var.inventory_source_account_id
  inventory_source_bucket_arn                = var.inventory_source_bucket_arn
  lifecycle_rule                             = var.lifecycle_rule
  logging                                    = try(var.logging.create || var.logging.target_bucket != null ? local.logging : {}, {})
  metric_configuration                       = var.metric_configuration
  object_lock_configuration                  = var.object_lock_configuration
  object_lock_enabled                        = var.object_lock_enabled
  object_ownership                           = "BucketOwnerPreferred"
  owner                                      = var.owner
  policy                                     = var.policy
  replication_configuration                  = var.replication_configuration
  request_payer                              = var.request_payer
  restrict_public_buckets                    = true
  server_side_encryption_configuration = {
    rule = {
      bucket_key_enabled = var.bucket_key_enabled
      apply_server_side_encryption_by_default = {
        kms_master_key_id = (var.sse_algorithm == "AES256" ? "" : var.kms_key_id)
        sse_algorithm     = coalesce(var.sse_algorithm, "aws:kms")
      }
    }
  }
  tags                                   = module.tags.tags
  transition_default_minimum_object_size = var.transition_default_minimum_object_size
  versioning                             = var.versioning
  website                                = var.website
}

module "s3_log_bucket" {
  source  = "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/s3-bucket/aws"
  version = "4.2.2"

  acceleration_status                        = var.s3_log_acceleration_status
  access_log_delivery_policy_source_accounts = var.s3_log_access_log_delivery_policy_source_accounts
  access_log_delivery_policy_source_buckets  = coalescelist(var.s3_log_access_log_delivery_policy_source_buckets, ["arn:aws:s3:::${var.bucket}"])
  attach_access_log_delivery_policy          = var.s3_log_attach_access_log_delivery_policy
  attach_deny_insecure_transport_policy      = true
  attach_elb_log_delivery_policy             = var.s3_log_attach_elb_log_delivery_policy
  attach_inventory_destination_policy        = var.s3_log_attach_inventory_destination_policy
  attach_lb_log_delivery_policy              = var.s3_log_attach_lb_log_delivery_policy
  attach_policy                              = var.s3_log_attach_policy
  attach_public_policy                       = var.s3_log_attach_public_policy
  attach_require_latest_tls_policy           = true
  block_public_acls                          = true
  block_public_policy                        = true
  bucket                                     = var.s3_log_bucket
  bucket_prefix                              = var.s3_log_bucket_prefix
  control_object_ownership                   = var.s3_log_control_object_ownership
  cors_rule                                  = var.s3_log_cors_rule
  create_bucket                              = var.logging.create
  expected_bucket_owner                      = var.s3_log_expected_bucket_owner
  force_destroy                              = var.s3_log_force_destroy
  grant                                      = var.s3_log_grant
  ignore_public_acls                         = true
  intelligent_tiering                        = var.s3_log_intelligent_tiering
  inventory_configuration                    = var.s3_log_inventory_configuration
  inventory_self_source_destination          = var.s3_log_inventory_self_source_destination
  inventory_source_account_id                = var.s3_log_inventory_source_account_id
  inventory_source_bucket_arn                = var.s3_log_inventory_source_bucket_arn
  lifecycle_rule                             = var.s3_log_lifecycle_rule
  logging                                    = var.s3_log_logging
  metric_configuration                       = var.s3_log_metric_configuration
  object_lock_configuration                  = var.s3_log_object_lock_configuration
  object_lock_enabled                        = var.s3_log_object_lock_enabled
  object_ownership                           = "BucketOwnerPreferred"
  owner                                      = var.s3_log_owner
  policy                                     = var.s3_log_policy
  replication_configuration                  = var.s3_log_replication_configuration
  request_payer                              = var.s3_log_request_payer
  restrict_public_buckets                    = true
  server_side_encryption_configuration = {
    rule = {
      apply_server_side_encryption_by_default = {
        kms_master_key_id = (var.s3_log_sse_algorithm == "AES256" ? "" : var.s3_log_kms_key_id)
        sse_algorithm     = coalesce(var.s3_log_sse_algorithm, "aws:kms")
      }
    }
  }
  tags                                   = merge(module.tags.tags, var.s3_log_tags)
  transition_default_minimum_object_size = var.s3_log_transition_default_minimum_object_size
  versioning                             = var.s3_log_versioning
  website                                = var.s3_log_website
}

#>>GENERATE_CONTENT

