#!/bin/bash
set -exo pipefail
catch() {
    aws ec2 create-tags --resources $instance_id --tags Key=${result_tag},Value=${failed_value}
    exit 1
}

# We install aws cli on our own since we need it for the tagging right from the start if anything
# goes south. For this reason we are not using the ansible job provided by customization.py script.
install_aws_cli() {
    sudo yum remove awscli -y
    mkdir -p /home/<USER>/.aws
    echo '
    [default]
    role_arn=arn:${partition}:iam::${account_no}:role/${role_name}
    credential_source=Ec2InstanceMetadata
    region=${region}
    ' > /home/<USER>/.aws/config

    chmod -R 777 /home/<USER>/.aws
    curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
    unzip awscliv2.zip

    sudo ./aws/install
    rm -rf awscliv2.zip ./aws
}
merckifications() {
    echo y | /opt/merck/bin/refresh_linux_microservices.sh
    # Enable/disable whatevere you need
    # Check this documentation https://share.merck.com/display/CIVT/Instructions+for+use+of+Image+Bakery+Linux+Image
    sed -i '/"name": "aws_ssm_agent_install"/{n;s/.*/       "enabled": "yes",/}' /etc/merck-services
    sudo /opt/merck/bin/customization.py
}

app_install(){
    echo "Your application instalation goes here"
}


init() {
    export instance_id=$(cat /var/lib/cloud/data/instance-id)
    trap 'catch' ERR
    trap "" HUP
    yum update -y

    # this is required to tag
    install_aws_cli
    merckifications
    app_install

    # If we haven't failed till now, let's decide the success based on the IQOQ result
    if sudo cat /root/*IQOQ* | grep FAIL ; then
        aws ec2 create-tags --resources $instance_id --tags Key=${result_tag},Value=${failed_value}
    else
        aws ec2 create-tags --resources $instance_id --tags Key=${result_tag},Value=${success_value}
    fi
}


export -f init install_aws_cli catch app_install merckifications

nohup bash -c init &
