#  S3 Bucket Wrapper example with Notification enabled

## Introduction
This is an example of the S3 bucket wrapper module usage with Notification enabled. This one deploys a S3 bucket which triggers a notification to a Lambda function.

## Pre-requisites
Before deploying this module you will need to have the following:

| | |
|------|---------|
|Terraform tool|Used to do the deployment based on the IaC code|
|AWS account|Needed to host the resources you are deploying|
|IAM user and role for deployment|Needed for authenticating with your AWS account and for deploying the resources there|
|:warning: Deployment permissions|The minimum required IAM permissions (least-privilege) for deploying this module can be found in the [iam](iam/) folder under `deployment_policy.json`. Add these permissions to your deployment role policy before deploying!|

## Usage 

The easiest way to leverage this example is to copy the contents of this folder to your application repository and perform the steps below.

#### (1) Update Application specific information
- Navigate to [backend.dev.conf](backend.dev.conf) (or [backend.dev-cn.conf](backend.dev-cn.conf) if you want to deploy to `aws-cn`) and update the values with your S3 backend configuration. ( [How to create the s3 backend can be found here.](https://github.com/merck-gen/iac-shared-cf-pattern-aws-remote-state-for-terraform))
- Navigate to [terraform.dev.tfvars](terraform.dev.tfvars) (or [terraform.dev-cn.tfvars](terraform.dev-cn.tfvars) if you want to deploy to `aws-cn`) and update the values for Tags, Common, and any AWS service specific values
- Review `main.tf` and update any **source** values to the latest released version of the module found in [IAC Shared Library releases](https://go.merck.com/iacreleases)

#### (2) Deployment
Run the following commands:
```
terraform init --backend-config backend.dev.conf
terraform plan --var-file terraform.dev.tfvars -out tfplan
terraform apply tfplan
```


Note that this example may create resources which can cost money (AWS Elastic IP, for example). Run `terraform destroy --var-file terraform.dev.tfvars` when you don't need these resources.


<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.2 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | >= 5.27 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_aws"></a> [aws](#provider\_aws) | >= 5.27 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_aws_kms_key_lambda"></a> [aws\_kms\_key\_lambda](#module\_aws\_kms\_key\_lambda) | artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/kms/aws | ~> 2.5 |
| <a name="module_aws_kms_key_s3_bucket"></a> [aws\_kms\_key\_s3\_bucket](#module\_aws\_kms\_key\_s3\_bucket) | artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/kms/aws | ~> 2.5 |
| <a name="module_cloudwatch_log_group"></a> [cloudwatch\_log\_group](#module\_cloudwatch\_log\_group) | artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/cloudwatch/aws//modules/log-group/ | ~> 2.2 |
| <a name="module_labels"></a> [labels](#module\_labels) | artifacts.merck.com/terraform-iac-shared__internal/labels/aws | ~> 4.1 |
| <a name="module_lambda"></a> [lambda](#module\_lambda) | artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/lambda/aws | ~> 4.3 |
| <a name="module_lambda_security_group"></a> [lambda\_security\_group](#module\_lambda\_security\_group) | artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/security-group/aws | ~> 2.5 |
| <a name="module_s3_bucket"></a> [s3\_bucket](#module\_s3\_bucket) | ../../ | n/a |
| <a name="module_s3_notification"></a> [s3\_notification](#module\_s3\_notification) | ../../modules/notification | n/a |

## Resources

| Name | Type |
|------|------|
| [aws_iam_policy_document.kms_default_primary](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |
| [aws_iam_policy_document.s3_bucket_policy](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document) | data source |
| [aws_partition.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/partition) | data source |
| [aws_region.current](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/region) | data source |
| [aws_vpc.this](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/vpc) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_account_no"></a> [account\_no](#input\_account\_no) | The application account number | `string` | `""` | no |
| <a name="input_allowed_kms_key_arn"></a> [allowed\_kms\_key\_arn](#input\_allowed\_kms\_key\_arn) | The ARN of KMS key which should be allowed in PutObject | `string` | `null` | no |
| <a name="input_attach_deny_incorrect_encryption_headers"></a> [attach\_deny\_incorrect\_encryption\_headers](#input\_attach\_deny\_incorrect\_encryption\_headers) | Controls if S3 bucket should deny incorrect encryption headers policy attached. | `bool` | `false` | no |
| <a name="input_attach_deny_incorrect_kms_key_sse"></a> [attach\_deny\_incorrect\_kms\_key\_sse](#input\_attach\_deny\_incorrect\_kms\_key\_sse) | Controls if S3 bucket policy should deny usage of incorrect KMS key SSE. | `bool` | `false` | no |
| <a name="input_attach_deny_unencrypted_object_uploads"></a> [attach\_deny\_unencrypted\_object\_uploads](#input\_attach\_deny\_unencrypted\_object\_uploads) | Controls if S3 bucket should deny unencrypted object uploads policy attached. | `bool` | `false` | no |
| <a name="input_attach_elb_log_delivery_policy"></a> [attach\_elb\_log\_delivery\_policy](#input\_attach\_elb\_log\_delivery\_policy) | Controls if S3 bucket should have ELB log delivery policy attached | `bool` | `false` | no |
| <a name="input_attach_lb_log_delivery_policy"></a> [attach\_lb\_log\_delivery\_policy](#input\_attach\_lb\_log\_delivery\_policy) | Controls if S3 bucket should have ALB/NLB log delivery policy attached | `bool` | `false` | no |
| <a name="input_attach_policy"></a> [attach\_policy](#input\_attach\_policy) | Controls if S3 bucket should have bucket policy attached (set to `true` to use value of `policy` as bucket policy) | `bool` | `false` | no |
| <a name="input_authorizer_create_package"></a> [authorizer\_create\_package](#input\_authorizer\_create\_package) | Controls whether Lambda package should be created | `bool` | `true` | no |
| <a name="input_authorizer_handler"></a> [authorizer\_handler](#input\_authorizer\_handler) | Lambda Function entrypoint in your code | `string` | `""` | no |
| <a name="input_authorizer_local_existing_package"></a> [authorizer\_local\_existing\_package](#input\_authorizer\_local\_existing\_package) | The absolute path to an existing zip-file to use | `string` | `null` | no |
| <a name="input_authorizer_publish"></a> [authorizer\_publish](#input\_authorizer\_publish) | Whether to publish creation/change as new Lambda Function Version. | `bool` | `false` | no |
| <a name="input_authorizer_runtime"></a> [authorizer\_runtime](#input\_authorizer\_runtime) | Lambda Function runtime | `string` | `""` | no |
| <a name="input_cloudwatch_logs_retention_in_days"></a> [cloudwatch\_logs\_retention\_in\_days](#input\_cloudwatch\_logs\_retention\_in\_days) | Specifies the number of days you want to retain log events in the specified log group. Possible values are: 1, 3, 5, 7, 14, 30, 60, 90, 120, 150, 180, 365, 400, 545, 731, 1827, and 3653. | `number` | `null` | no |
| <a name="input_default_tags"></a> [default\_tags](#input\_default\_tags) | A map of default tags to be applied on each resource. You can get required tags [here](https://cloud.merck.com/documentation/compliance/tagging-standards/index.html) | `map(string)` | n/a | yes |
| <a name="input_deployment_role"></a> [deployment\_role](#input\_deployment\_role) | The application deployment role | `string` | `""` | no |
| <a name="input_kms_key_id"></a> [kms\_key\_id](#input\_kms\_key\_id) | KMS Key for S3 | `string` | `null` | no |
| <a name="input_lambda_authorizer_description"></a> [lambda\_authorizer\_description](#input\_lambda\_authorizer\_description) | Lambda function usage description | `string` | `""` | no |
| <a name="input_partition"></a> [partition](#input\_partition) | Second part of aws arn. Used only in the provider configuration. | `string` | `"aws"` | no |
| <a name="input_region"></a> [region](#input\_region) | AWS Region | `string` | `"us-east-1"` | no |
| <a name="input_resource_vars"></a> [resource\_vars](#input\_resource\_vars) | map of tags to pass to labels module | <pre>object({<br/>    appname     = string<br/>    region      = string<br/>    environment = string<br/>    attributes  = list(string)<br/>    label_order = list(string)<br/>  })</pre> | <pre>{<br/>  "appname": "myapp",<br/>  "attributes": [<br/>    "myattr"<br/>  ],<br/>  "environment": "dev",<br/>  "label_order": [<br/>    "appname",<br/>    "region"<br/>  ],<br/>  "region": "us1"<br/>}</pre> | no |
| <a name="input_s3_bucket_cors_rule"></a> [s3\_bucket\_cors\_rule](#input\_s3\_bucket\_cors\_rule) | List of maps containing rules for Cross-Origin Resource Sharing. | `any` | `[]` | no |
| <a name="input_s3_bucket_intelligent_tiering"></a> [s3\_bucket\_intelligent\_tiering](#input\_s3\_bucket\_intelligent\_tiering) | Map containing intelligent tiering configuration. | `any` | `{}` | no |
| <a name="input_s3_bucket_lifecycle_rule"></a> [s3\_bucket\_lifecycle\_rule](#input\_s3\_bucket\_lifecycle\_rule) | List of maps containing configuration of object lifecycle management. | `any` | `[]` | no |
| <a name="input_s3_bucket_versioning"></a> [s3\_bucket\_versioning](#input\_s3\_bucket\_versioning) | Map containing versioning configuration. Use terraform.auto.tfvars file | `map(string)` | <pre>{<br/>  "mfa_delete": "disabled",<br/>  "status": "enabled"<br/>}</pre> | no |
| <a name="input_sse_algorithm"></a> [sse\_algorithm](#input\_sse\_algorithm) | KMS Key for S3 | `string` | `"aws_kms"` | no |
| <a name="input_subnet_ids"></a> [subnet\_ids](#input\_subnet\_ids) | List of all the VPC id | `list(string)` | n/a | yes |
| <a name="input_tags"></a> [tags](#input\_tags) | map of tags to pass to labels module | `map(string)` | `{}` | no |
| <a name="input_vpc_id"></a> [vpc\_id](#input\_vpc\_id) | VPC id | `string` | n/a | yes |
| <a name="input_website"></a> [website](#input\_website) | Map containing static web-site hosting or redirect configuration. | `any` | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_key_arn"></a> [key\_arn](#output\_key\_arn) | The Amazon Resource Name (ARN) of the key |
| <a name="output_key_id"></a> [key\_id](#output\_key\_id) | The globally unique identifier for the key |
| <a name="output_key_policy"></a> [key\_policy](#output\_key\_policy) | The IAM resource policy set on the key |
| <a name="output_lambda"></a> [lambda](#output\_lambda) | Map of outputs from the lambda wrapper module |
| <a name="output_log_bucket_arn"></a> [log\_bucket\_arn](#output\_log\_bucket\_arn) | The arn of the log bucket. |
| <a name="output_log_bucket_id"></a> [log\_bucket\_id](#output\_log\_bucket\_id) | The name off the log bucket created. |
| <a name="output_s3_bucket_arn"></a> [s3\_bucket\_arn](#output\_s3\_bucket\_arn) | The arn of the bucket. |
| <a name="output_s3_bucket_id"></a> [s3\_bucket\_id](#output\_s3\_bucket\_id) | The name of the bucket. |
| <a name="output_s3_bucket_notification_id"></a> [s3\_bucket\_notification\_id](#output\_s3\_bucket\_notification\_id) | The key of S3 object |
<!-- END_TF_DOCS -->
