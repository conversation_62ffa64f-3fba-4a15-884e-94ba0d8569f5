{"Statement": [{"Action": ["dynamodb:DeleteItem", "dynamodb:GetItem", "dynamodb:PutItem", "iam:AttachRolePolicy", "iam:CreatePolicy", "iam:CreateRole", "iam:DeletePolicy", "iam:DeleteRole", "iam:DetachRolePolicy", "iam:GetPolicy", "iam:GetPolicyVersion", "iam:GetRole", "iam:ListAttachedRolePolicies", "iam:ListEntitiesForPolicy", "iam:ListInstanceProfilesForRole", "iam:ListPolicyVersions", "iam:ListRolePolicies", "iam:PassRole", "iam:TagPolicy", "iam:TagRole", "iam:UntagPolicy", "iam:UntagRole", "kms:<PERSON><PERSON><PERSON><PERSON><PERSON>", "kms:C<PERSON><PERSON><PERSON>", "kms:DeleteAlias", "kms:ListAliases", "kms:TagResource", "s3:CreateBucket", "s3:DeleteBucket", "s3:DeleteBucketPolicy", "s3:DeleteObject", "s3:DeleteObjectVersion", "s3:GetAccelerateConfiguration", "s3:GetBucketAcl", "s3:GetBucketCORS", "s3:GetBucketLogging", "s3:GetBucketObjectLockConfiguration", "s3:GetBucketPolicy", "s3:GetBucketPublicAccessBlock", "s3:GetBucketRequestPayment", "s3:GetBucketTagging", "s3:GetBucketVersioning", "s3:GetBucketWebsite", "s3:GetEncryptionConfiguration", "s3:GetLifecycleConfiguration", "s3:GetObject", "s3:GetReplicationConfiguration", "s3:ListBucket", "s3:ListBucketVersions", "s3:PutBucketLogging", "s3:PutBucketPolicy", "s3:PutBucketPublicAccessBlock", "s3:PutBucketTagging", "s3:PutBucketVersioning", "s3:PutEncryptionConfiguration", "s3:PutObject", "s3:PutReplicationConfiguration", "sts:<PERSON><PERSON>Role"], "Effect": "Allow", "Resource": "*"}], "Version": "2012-10-17"}