#>>GENERATE_CONTENT
module "ext" {
  source  = "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/sns/aws"
  version = "6.1.2"

  application_feedback            = var.application_feedback
  archive_policy                  = var.archive_policy
  content_based_deduplication     = var.content_based_deduplication
  create                          = true
  create_subscription             = var.create_subscription
  create_topic_policy             = var.create_topic_policy
  data_protection_policy          = var.data_protection_policy
  delivery_policy                 = var.delivery_policy
  display_name                    = var.display_name
  enable_default_topic_policy     = var.enable_default_topic_policy
  fifo_topic                      = var.fifo_topic
  firehose_feedback               = var.firehose_feedback
  http_feedback                   = var.http_feedback
  kms_master_key_id               = var.kms_master_key_id
  lambda_feedback                 = var.lambda_feedback
  name                            = var.name
  override_topic_policy_documents = var.override_topic_policy_documents
  signature_version               = var.signature_version
  source_topic_policy_documents   = var.source_topic_policy_documents
  sqs_feedback                    = var.sqs_feedback
  subscriptions                   = var.subscriptions
  tags                            = module.tags.tags
  topic_policy                    = var.topic_policy
  topic_policy_statements         = var.topic_policy_statements
  tracing_config                  = var.tracing_config
  use_name_prefix                 = var.use_name_prefix
}

#>>GENERATE_CONTENT