# Outputs
########## S3 Object ##########
output "s3_object_id" {
  description = "The key of S3 object"
  value       = module.s3_object.s3_object_id
}

output "s3_object_etag" {
  description = "The ETag generated for the object (an MD5 sum of the object content)."
  value       = module.s3_object.s3_object_etag
}

output "s3_object_version_id" {
  description = "A unique version ID value for the object, if bucket versioning is enabled."
  value       = module.s3_object.s3_object_version_id
}

########## S3 Bucket ##########
output "s3_bucket_id" {
  description = "The name of the bucket."
  value       = module.s3_bucket.s3_bucket_id
}

output "s3_bucket_arn" {
  description = "The arn of the bucket."
  value       = module.s3_bucket.s3_bucket_arn
}

########## S3 Log Bucket ##########
output "log_bucket_id" {
  description = "The name off the log bucket created."
  value       = module.s3_bucket.s3_log_bucket_id
}

output "log_bucket_arn" {
  description = "The arn of the log bucket."
  value       = module.s3_bucket.s3_log_bucket_arn
}

########## KMS Key for S3 Bucket ##########
output "key_arn" {
  description = "The Amazon Resource Name (ARN) of the key"
  value       = module.aws_kms_key_primary.key_arn
}

output "key_id" {
  description = "The globally unique identifier for the key"
  value       = module.aws_kms_key_primary.key_id
}

output "key_policy" {
  description = "The IAM resource policy set on the key"
  value       = module.aws_kms_key_primary.key_policy
}