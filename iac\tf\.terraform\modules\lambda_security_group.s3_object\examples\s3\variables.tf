variable "default_tags" {
  description = "A map of default tags to be applied on each resource. You can get required tags [here](https://cloud.merck.com/documentation/compliance/tagging-standards/index.html)"
  type        = map(string)
}

variable "tags" {
  description = "map of tags to pass to labels module"
  type        = map(string)
  default     = {}
}

variable "resource_vars" {
  description = "map of tags to pass to labels module"
  type = object({
    appname     = string
    region      = string
    attributes  = list(string)
    label_order = list(string)
    environment = optional(string, "dev")
  })
}

variable "region" {
  description = "AWS Region"
  type        = string
  default     = "us-east-1"
}

variable "partition" {
  type        = string
  default     = "aws"
  description = "Second part of aws arn. Used only in the provider configuration."
}

variable "deployment_role" {
  description = "Terraform Deployment Role"
  type        = string
}

variable "account_no" {
  description = "AWS account number to deploy to"
  type        = string
}

variable "attach_elb_log_delivery_policy" {
  description = "Controls if S3 bucket should have ELB log delivery policy attached"
  type        = bool
  default     = false
}

variable "attach_lb_log_delivery_policy" {
  description = "Controls if S3 bucket should have ALB/NLB log delivery policy attached"
  type        = bool
  default     = false
}

variable "attach_policy" {
  description = "Controls if S3 bucket should have bucket policy attached (set to `true` to use value of `policy` as bucket policy)"
  type        = bool
  default     = false
}

variable "attach_deny_incorrect_encryption_headers" {
  description = "Controls if S3 bucket should deny incorrect encryption headers policy attached."
  type        = bool
  default     = false
}
variable "attach_deny_incorrect_kms_key_sse" {
  description = "Controls if S3 bucket policy should deny usage of incorrect KMS key SSE."
  type        = bool
  default     = false
}
variable "allowed_kms_key_arn" {
  description = "The ARN of KMS key which should be allowed in PutObject"
  type        = string
  default     = null
}
variable "attach_deny_unencrypted_object_uploads" {
  description = "Controls if S3 bucket should deny unencrypted object uploads policy attached."
  type        = bool
  default     = false
}

variable "website" {
  description = "Map containing static web-site hosting or redirect configuration."
  type        = any # map(string)
  default     = {}
}

### S3 Bucket Variables ###
variable "s3_bucket_versioning" {
  description = "Map containing versioning configuration. Use terraform.auto.tfvars file"
  type        = map(string)
  default = {
    status     = "enabled"
    mfa_delete = "disabled"
  }
}
variable "s3_bucket_cors_rule" {
  description = "List of maps containing rules for Cross-Origin Resource Sharing."
  type        = any
  default     = []
}
variable "s3_bucket_lifecycle_rule" {
  description = "List of maps containing configuration of object lifecycle management."
  type        = any
  default     = []
}
variable "s3_bucket_intelligent_tiering" {
  description = "Map containing intelligent tiering configuration."
  type        = any
  default     = {}
}

variable "force_destroy" {
  description = "(Optional, Default:false ) A boolean that indicates all objects should be deleted from the bucket so that the bucket can be destroyed without error. These objects are not recoverable."
  type        = bool
  default     = false
}

variable "kms_key_id" {
  description = "KMS Key for S3"
  type        = string
  default     = null
}

variable "sse_algorithm" {
  description = "KMS Key for S3"
  type        = string
  default     = "aws_kms"
}

variable "kms_use_multi_region" {
  description = "A switch to turn multi_region on/off for kms"
  type        = bool
  default     = true
}
