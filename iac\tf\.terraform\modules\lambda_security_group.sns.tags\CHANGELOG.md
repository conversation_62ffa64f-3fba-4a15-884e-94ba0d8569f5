# Changelog
​
All notable changes to this repository will be documented in this file.
​
The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

Please use the boilerplate code below to fill in information related to your change (create a new section for each new version).

## [Unreleased]

## [1.0.0] - 2024-02-08

### Removed 

- `extra_tags` variable
  
### Changed 

- more conditions to handle edge cases in `main.tf`
- handling of user given custom tags
- **previous behavior:** input_tags were ignored if they didn't contain IacModule or IacPattern tag -> **new behavior:** Now the input tags are taken into account and no need for `extra_tags` variable.
- `input_tags` can be used for passing all of the tags.
  
## [0.2.0] - 2023-10-12

### Added 

- default tags variable
- extra tags variable

### Changed 

- main.tf

## [0.1.1] - 2023-08-16

### Changed 
- main.tf

## [0.1.0] - 2023-08-07

### Added
- main.tf
- output.tf
- versions.tf
- variables.tf
- README.md
- CHANGELOG.md
