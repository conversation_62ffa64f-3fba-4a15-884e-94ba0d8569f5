provider "aws" {
  region = var.region
  assume_role {
    role_arn = "arn:${var.partition}:iam::${var.account_no}:role/${var.deployment_role}"
  }
  default_tags {
    tags = var.default_tags
  }
}

provider "aws" {
  alias  = "replica_region"
  region = var.replica_region
  default_tags {
    tags = var.default_tags
  }
  assume_role {
    # The role ARN within Account to AssumeRole into..
    role_arn = "arn:${var.partition}:iam::${var.account_no}:role/${var.deployment_role}"
  }
}

