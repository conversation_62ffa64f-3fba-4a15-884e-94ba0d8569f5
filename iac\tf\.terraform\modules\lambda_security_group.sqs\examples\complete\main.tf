###############################
# Labels Module               #
###############################
module "labels" {
  source  = "artifacts.merck.com/terraform-iac-shared__internal/labels/aws"
  version = "~> 4.0"
  enabled = true

  appname     = var.resource_vars.appname
  region      = var.resource_vars.region
  attributes  = var.resource_vars.attributes
  label_order = var.resource_vars.label_order
  environment = var.resource_vars.environment
}


################################
# SQS Module                   #
################################
module "sqs" {
  # Change the below source to the proper latest wrapper release found @ https://go.merck.com/iacreleases
  source = "../../"

  content_based_deduplication           = var.content_based_deduplication
  deduplication_scope                   = var.deduplication_scope
  delay_seconds                         = var.delay_seconds
  fifo_queue                            = var.fifo_queue
  fifo_throughput_limit                 = var.fifo_throughput_limit
  kms_data_key_reuse_period_seconds     = var.kms_data_key_reuse_period_seconds
  kms_master_key_id                     = module.kms_key.key_arn
  max_message_size                      = var.max_message_size
  message_retention_seconds             = var.message_retention_seconds
  name                                  = module.labels.id
  use_name_prefix                       = var.use_name_prefix
  receive_wait_time_seconds             = var.receive_wait_time_seconds
  redrive_allow_policy                  = var.redrive_allow_policy
  redrive_policy                        = var.redrive_policy
  visibility_timeout_seconds            = var.visibility_timeout_seconds
  tags                                  = var.default_tags
  default_tags                          = var.default_tags
  source_queue_policy_documents         = var.source_queue_policy_documents
  override_queue_policy_documents       = var.override_queue_policy_documents
  queue_policy_statements               = var.queue_policy_statements
  create_dlq                            = var.create_dlq
  dlq_content_based_deduplication       = var.dlq_content_based_deduplication
  dlq_deduplication_scope               = var.dlq_deduplication_scope
  dlq_delay_seconds                     = var.dlq_delay_seconds
  dlq_kms_data_key_reuse_period_seconds = var.dlq_kms_data_key_reuse_period_seconds
  dlq_kms_master_key_id                 = var.dlq_kms_master_key_id
  dlq_message_retention_seconds         = var.dlq_message_retention_seconds
  dlq_name                              = "${module.labels.id}-dlq"
  dlq_receive_wait_time_seconds         = var.dlq_receive_wait_time_seconds
  create_dlq_redrive_allow_policy       = var.create_dlq_redrive_allow_policy
  dlq_redrive_allow_policy              = var.dlq_redrive_allow_policy
  dlq_sqs_managed_sse_enabled           = var.dlq_sqs_managed_sse_enabled
  dlq_visibility_timeout_seconds        = var.dlq_visibility_timeout_seconds
  dlq_tags                              = var.default_tags
  create_dlq_queue_policy               = var.create_dlq_queue_policy
  source_dlq_queue_policy_documents     = var.source_dlq_queue_policy_documents
  override_dlq_queue_policy_documents   = var.override_dlq_queue_policy_documents
  dlq_queue_policy_statements           = var.dlq_queue_policy_statements
}

################################
# KMS Module                   #
################################
module "kms_key" {
  source       = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/kms/aws"
  version      = "~> 2.4"
  default_tags = var.default_tags
  description  = "KMS key for ${module.labels.id}"
  key_usage    = "ENCRYPT_DECRYPT"
  tags         = var.default_tags
  aliases      = ["alias/${module.labels.id}"]
}
