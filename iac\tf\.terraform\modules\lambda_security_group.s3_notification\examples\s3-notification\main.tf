data "aws_region" "current" {}

data "aws_partition" "current" {}

###############################
# Labels Module               #
###############################
module "labels" {
  source  = "artifacts.merck.com/terraform-iac-shared__internal/labels/aws"
  version = "~> 4.1"
  enabled = true

  appname     = var.resource_vars.appname
  region      = var.resource_vars.region
  attributes  = var.resource_vars.attributes
  label_order = var.resource_vars.label_order
  environment = var.resource_vars.environment
}

################################
# S3 Notification with SNS     #
################################
module "s3_notification" {
  # Change the below source to the proper latest wrapper release found @ https://go.merck.com/iacreleases
  # IMPORTANT: for S3 notification you will need to reference the notification wrapper-module specifically
  ### Example: source = "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-s3-bucket-wrapper_1.6.0.tgz//wrapper-modules/notification"
  source = "../../modules/notification"
  bucket = module.s3_bucket.s3_bucket_id
  sns_notifications = {
    sns1 = {
      topic_arn = module.sns_topic.topic_arn
      events    = ["s3:ObjectCreated:*"]
    }
  }
  create_sqs_policy = false # This example is for SNS so no need to create a SQS policy
}

#########################################
# S3 Bucket (to add notification to)    #
# Disregard if you have existing bucket #
#########################################
module "s3_bucket" {
  # Change the below source to the proper latest wrapper release found @ https://go.merck.com/iacreleases
  source  = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/s3-bucket/aws"
  version = "3.3.0"

  create_bucket                            = true
  bucket                                   = "${module.labels.id}-bucket"
  policy                                   = data.aws_iam_policy_document.s3_bucket_policy.json
  cors_rule                                = var.s3_bucket_cors_rule
  lifecycle_rule                           = var.s3_bucket_lifecycle_rule
  intelligent_tiering                      = var.s3_bucket_intelligent_tiering
  tags                                     = var.tags
  default_tags                             = var.default_tags
  attach_deny_incorrect_encryption_headers = var.attach_deny_incorrect_encryption_headers
  attach_deny_incorrect_kms_key_sse        = var.attach_deny_incorrect_kms_key_sse
  allowed_kms_key_arn                      = var.allowed_kms_key_arn
  attach_deny_unencrypted_object_uploads   = var.attach_deny_unencrypted_object_uploads
  versioning                               = var.s3_bucket_versioning
  attach_policy                            = var.attach_elb_log_delivery_policy || var.attach_lb_log_delivery_policy || var.attach_policy
  website                                  = var.website
  kms_key_id                               = (var.kms_key_id != "" ? var.kms_key_id : module.aws_kms_key_s3_bucket.key_arn)
  sse_algorithm                            = var.sse_algorithm
  bucket_key_enabled                       = true
  replication_configuration                = {}
  force_destroy                            = true #for testing purposes
  s3_log_bucket                            = "${module.labels.id}-log-bucket"
  s3_log_force_destroy                     = true #for testing purposes
  # logging = {
  #   create = false
  # }
}

################################
# Supporting Resources         #
################################

########## SNS Topic ###########
module "sns_topic" {
  source  = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/sns/aws"
  version = "~> 2.2"
  name    = var.sns_name
  subscriptions = {
    key_name = {
      endpoint  = "<EMAIL>", #Required. Endpoint to send data to. Contents vary with the protocol.
      protocol  = "email",                       #Required. Valid values are: sqs, sms, lambda, firehose, application, email, email-json, http, https
      topic_arn = module.sns_topic.topic_arn     #Provide name of topic to which subscription needs to be associated with.
    }
  }

  tags                          = var.tags
  default_tags                  = var.default_tags
  kms_master_key_id             = module.aws_kms_key_sns_topic.key_id
  source_topic_policy_documents = [data.aws_iam_policy_document.sns_topic_policy.json]
}

########## KMS Key - SNS Topic ###########
module "aws_kms_key_sns_topic" {
  source       = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/kms/aws"
  version      = "~> 2.5"
  description  = "CMK for primary region"
  tags         = var.tags
  default_tags = var.default_tags

  policy  = data.aws_iam_policy_document.kms_default_primary.json
  aliases = ["${module.labels.id}-kms-sns-topic"]
}

########## KMS Key - S3 Bucket ###########
module "aws_kms_key_s3_bucket" {
  source       = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/kms/aws"
  version      = "~> 2.5"
  description  = "CMK for primary region"
  tags         = var.tags
  default_tags = var.default_tags

  policy  = data.aws_iam_policy_document.kms_default_primary.json
  aliases = ["${module.labels.id}-kms-s3-bucket"]
}

########## IAM Policy - SNS Topic ###########
data "aws_iam_policy_document" "sns_topic_policy" {
  statement {
    sid       = "sns_topic_policy1"
    actions   = ["SNS:Publish"]
    effect    = "Allow"
    resources = ["arn:${data.aws_partition.current.partition}:sns:${var.region}:${var.account_no}:*"]
    principals {
      type        = "Service"
      identifiers = ["sns.amazonaws.com", "s3.amazonaws.com"]
    }
  }
}

########## IAM Policy - S3 Bucket ###########
data "aws_iam_policy_document" "s3_bucket_policy" {
  statement {
    effect = "Allow"
    actions = [
      "s3:ListBucket",
      "sns:Publish"
    ]
    resources = ["arn:${data.aws_partition.current.partition}:sns:${var.region}:${var.account_no}:*"]
  }
}
