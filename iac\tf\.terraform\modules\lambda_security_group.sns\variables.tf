#>>GENERATE_CONTENT

variable "default_tags" {
  type        = map(string)
  description = "A map of default tags to be applied on each resource. You can get required tags [here](https://cloud.merck.com/documentation/compliance/tagging-standards/index.html)"
  validation {
    condition     = length(setintersection(keys(var.default_tags), ["DataClassification", "Consumer", "Application", "Environment", "Service"])) >= length(["DataClassification", "Consumer", "Application", "Environment", "Service"])
    error_message = "Keys: DataClassification, Consumer, Application, Environment, Service are required!"
  }
}

variable "application_feedback" {
  type        = map(string)
  default     = {}
  description = "Map of IAM role ARNs and sample rate for success and failure feedback"
}

variable "archive_policy" {
  type        = string
  default     = null
  description = "The message archive policy for FIFO topics."
}

variable "content_based_deduplication" {
  type        = bool
  default     = false
  description = "Boolean indicating whether or not to enable content-based deduplication for FIFO topics."
}

variable "create_subscription" {
  type        = bool
  default     = true
  description = "Determines whether an SNS subscription is created"
}

variable "create_topic_policy" {
  type        = bool
  default     = true
  description = "Determines whether an SNS topic policy is created"
}

variable "data_protection_policy" {
  type        = string
  default     = null
  description = "A map of data protection policy statements"
}

variable "delivery_policy" {
  type        = string
  default     = null
  description = "The SNS delivery policy"
}

variable "display_name" {
  type        = string
  default     = null
  description = "The display name for the SNS topic"
}

variable "enable_default_topic_policy" {
  type        = bool
  default     = true
  description = "Specifies whether to enable the default topic policy. Defaults to `true`"
}

variable "fifo_topic" {
  type        = bool
  default     = false
  description = "Boolean indicating whether or not to create a FIFO (first-in-first-out) topic"
}

variable "firehose_feedback" {
  type        = map(string)
  default     = {}
  description = "Map of IAM role ARNs and sample rate for success and failure feedback"
}

variable "http_feedback" {
  type        = map(string)
  default     = {}
  description = "Map of IAM role ARNs and sample rate for success and failure feedback"
}

variable "kms_master_key_id" {
  type        = string
  description = "The ID of an AWS-managed customer master key (CMK) for Amazon SNS or a custom CMK"
}

variable "lambda_feedback" {
  type        = map(string)
  default     = {}
  description = "Map of IAM role ARNs and sample rate for success and failure feedback"
}

variable "name" {
  type        = string
  default     = null
  description = "The name of the SNS topic to create"
}

variable "override_topic_policy_documents" {
  type        = list(string)
  default     = []
  description = "List of IAM policy documents that are merged together into the exported document. In merging, statements with non-blank `sid`s will override statements with the same `sid`"
}

variable "signature_version" {
  type        = number
  default     = null
  description = "If SignatureVersion should be 1 (SHA1) or 2 (SHA256). The signature version corresponds to the hashing algorithm used while creating the signature of the notifications, subscription confirmations, or unsubscribe confirmation messages sent by Amazon SNS."
}

variable "source_topic_policy_documents" {
  type        = list(string)
  default     = []
  description = "List of IAM policy documents that are merged together into the exported document. Statements must have unique `sid`s"
}

variable "sqs_feedback" {
  type        = map(string)
  default     = {}
  description = "Map of IAM role ARNs and sample rate for success and failure feedback"
}

variable "subscriptions" {
  type        = any
  default     = {}
  description = "A map of subscription definitions to create"
}

variable "tags" {
  type        = map(string)
  default     = {}
  description = "A map of tags to add to all resources"
}

variable "topic_policy" {
  type        = string
  default     = null
  description = "An externally created fully-formed AWS policy as JSON"
}

variable "topic_policy_statements" {
  type        = any
  default     = {}
  description = "A map of IAM policy [statements](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document#statement) for custom permission usage"
}

variable "tracing_config" {
  type        = string
  default     = null
  description = "Tracing mode of an Amazon SNS topic. Valid values: PassThrough, Active."
}

variable "use_name_prefix" {
  type        = bool
  default     = false
  description = "Determines whether `name` is used as a prefix"
}
#>>GENERATE_CONTENT