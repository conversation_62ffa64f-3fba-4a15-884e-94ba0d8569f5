data "aws_iam_policy_document" "kms_default_primary" {
  # Copy of default KMS policy that lets you manage it
  statement {
    sid       = "Enable IAM User Permissions"
    actions   = ["kms:*"]
    resources = ["arn:${data.aws_partition.current.partition}:kms:${data.aws_region.current.name}:${var.account_no}:key/*"]
    principals {
      type        = "AWS"
      identifiers = ["arn:${data.aws_partition.current.partition}:iam::${var.account_no}:root", "arn:${data.aws_partition.current.partition}:iam::${var.account_no}:role/${var.deployment_role}"]
    }
  }
  statement {
    sid = "Allow Cloudwatch service use of the CMK"
    actions = [
      "kms:Encrypt",
      "kms:Decrypt",
      "kms:ReEncrypt*",
      "kms:GenerateDataKey*",
      "kms:DescribeKey"
    ]
    resources = ["arn:${data.aws_partition.current.partition}:kms:${data.aws_region.current.name}:${var.account_no}:key/*"]
    principals {
      type = "Service"
      identifiers = [
        "logs.${var.region}.amazonaws.com"
      ]
    }
  }
}
