#  SNS Wrapper Module

[![CI Pipeline](https://github.com/merck-gen/iac-shared-tf-module-aws-sns-wrapper/actions/workflows/branch-ci.yaml/badge.svg)](https://github.com/merck-gen/iac-shared-tf-module-aws-sns-wrapper/actions/workflows/branch-ci.yaml) [![Terratest](https://github.com/merck-gen/iac-shared-tf-module-aws-sns-wrapper/actions/workflows/terratest.yaml/badge.svg)](https://github.com/merck-gen/iac-shared-tf-module-aws-sns-wrapper/actions/workflows/terratest.yaml)

## Introduction
This is the Merck wrapper for [SNS community module](https://github.com/merck-gen/iac-shared-tf-module-aws-sns-ext). The purpose of this module is to help you deploy a compliant [SNS topic](https://cloud.merck.com/documentation/native-services/aws/sns/index.html) in your AWS account.

## Pre-requisites
Before deploying this module you will need to have the following:

| | |
|------|---------|
|Terraform tool|Used to do the deployment based on the IaC code|
|AWS account|Needed to host the resources you are deploying|
|IAM user and role for deployment|Needed for authenticating with your AWS account and for deploying the resources there|
|Deployment permissions|The needed IAM permissions for deploying this module can be found from iam folder in root level or inside examples folder.|

| :warning:  Note!        |
|:---------------------------|
| Please check what is the latest available release version of this module: [IAC Shared library releases](https://go.merck.com/iacreleases)     |




## Usage

|  |  |
|------|---------|
|Examples|Please check the [examples](examples/) folder to see how this module is used.|
|Configurations|Anything listed in the [Inputs section of this readme file](#inputs) can be configured by the users. The module has some hardcoded values based on the [company security requirements](https://cloud.merck.com/documentation/native-services/aws/sns/) which the user can't change. You can find the hardcoded values in [main.tf](main.tf).|

## Contributing
Everyone is welcome to contribute to the code of this module! Please see the [instructions on how to contribute on IAC Shared home page](https://go.merck.com/iac).

## Contact
In case any issues regarding the module please comment on the [MS Teams Infrastructure as Code channel](https://teams.microsoft.com/l/channel/19%3acc5325594d1543069ea7270bcd393da1%40thread.skype/Infrastructure%2520as%2520Code?groupId=7d021e32-5295-41c2-ad7c-24f0f6de8ff4&tenantId=a00de4ec-48a8-43a6-be74-e31274e2060d) or connect via email <<EMAIL>>.

<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | >= 5.0 |
| <a name="requirement_local"></a> [local](#requirement\_local) | >= 2.4.0 |

## Providers

| Name | Version |
|------|---------|
| <a name="provider_local"></a> [local](#provider\_local) | >= 2.4.0 |

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_ext"></a> [ext](#module\_ext) | artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/sns/aws | 6.1.2 |
| <a name="module_tags"></a> [tags](#module\_tags) | https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz | n/a |

## Resources

| Name | Type |
|------|------|
| [local_file.version](https://registry.terraform.io/providers/hashicorp/local/latest/docs/data-sources/file) | data source |

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_application_feedback"></a> [application\_feedback](#input\_application\_feedback) | Map of IAM role ARNs and sample rate for success and failure feedback | `map(string)` | `{}` | no |
| <a name="input_archive_policy"></a> [archive\_policy](#input\_archive\_policy) | The message archive policy for FIFO topics. | `string` | `null` | no |
| <a name="input_content_based_deduplication"></a> [content\_based\_deduplication](#input\_content\_based\_deduplication) | Boolean indicating whether or not to enable content-based deduplication for FIFO topics. | `bool` | `false` | no |
| <a name="input_create_subscription"></a> [create\_subscription](#input\_create\_subscription) | Determines whether an SNS subscription is created | `bool` | `true` | no |
| <a name="input_create_topic_policy"></a> [create\_topic\_policy](#input\_create\_topic\_policy) | Determines whether an SNS topic policy is created | `bool` | `true` | no |
| <a name="input_data_protection_policy"></a> [data\_protection\_policy](#input\_data\_protection\_policy) | A map of data protection policy statements | `string` | `null` | no |
| <a name="input_default_tags"></a> [default\_tags](#input\_default\_tags) | A map of default tags to be applied on each resource. You can get required tags [here](https://cloud.merck.com/documentation/compliance/tagging-standards/index.html) | `map(string)` | n/a | yes |
| <a name="input_delivery_policy"></a> [delivery\_policy](#input\_delivery\_policy) | The SNS delivery policy | `string` | `null` | no |
| <a name="input_display_name"></a> [display\_name](#input\_display\_name) | The display name for the SNS topic | `string` | `null` | no |
| <a name="input_enable_default_topic_policy"></a> [enable\_default\_topic\_policy](#input\_enable\_default\_topic\_policy) | Specifies whether to enable the default topic policy. Defaults to `true` | `bool` | `true` | no |
| <a name="input_fifo_topic"></a> [fifo\_topic](#input\_fifo\_topic) | Boolean indicating whether or not to create a FIFO (first-in-first-out) topic | `bool` | `false` | no |
| <a name="input_firehose_feedback"></a> [firehose\_feedback](#input\_firehose\_feedback) | Map of IAM role ARNs and sample rate for success and failure feedback | `map(string)` | `{}` | no |
| <a name="input_http_feedback"></a> [http\_feedback](#input\_http\_feedback) | Map of IAM role ARNs and sample rate for success and failure feedback | `map(string)` | `{}` | no |
| <a name="input_kms_master_key_id"></a> [kms\_master\_key\_id](#input\_kms\_master\_key\_id) | The ID of an AWS-managed customer master key (CMK) for Amazon SNS or a custom CMK | `string` | n/a | yes |
| <a name="input_lambda_feedback"></a> [lambda\_feedback](#input\_lambda\_feedback) | Map of IAM role ARNs and sample rate for success and failure feedback | `map(string)` | `{}` | no |
| <a name="input_name"></a> [name](#input\_name) | The name of the SNS topic to create | `string` | `null` | no |
| <a name="input_override_topic_policy_documents"></a> [override\_topic\_policy\_documents](#input\_override\_topic\_policy\_documents) | List of IAM policy documents that are merged together into the exported document. In merging, statements with non-blank `sid`s will override statements with the same `sid` | `list(string)` | `[]` | no |
| <a name="input_signature_version"></a> [signature\_version](#input\_signature\_version) | If SignatureVersion should be 1 (SHA1) or 2 (SHA256). The signature version corresponds to the hashing algorithm used while creating the signature of the notifications, subscription confirmations, or unsubscribe confirmation messages sent by Amazon SNS. | `number` | `null` | no |
| <a name="input_source_topic_policy_documents"></a> [source\_topic\_policy\_documents](#input\_source\_topic\_policy\_documents) | List of IAM policy documents that are merged together into the exported document. Statements must have unique `sid`s | `list(string)` | `[]` | no |
| <a name="input_sqs_feedback"></a> [sqs\_feedback](#input\_sqs\_feedback) | Map of IAM role ARNs and sample rate for success and failure feedback | `map(string)` | `{}` | no |
| <a name="input_subscriptions"></a> [subscriptions](#input\_subscriptions) | A map of subscription definitions to create | `any` | `{}` | no |
| <a name="input_tags"></a> [tags](#input\_tags) | A map of tags to add to all resources | `map(string)` | `{}` | no |
| <a name="input_topic_policy"></a> [topic\_policy](#input\_topic\_policy) | An externally created fully-formed AWS policy as JSON | `string` | `null` | no |
| <a name="input_topic_policy_statements"></a> [topic\_policy\_statements](#input\_topic\_policy\_statements) | A map of IAM policy [statements](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document#statement) for custom permission usage | `any` | `{}` | no |
| <a name="input_tracing_config"></a> [tracing\_config](#input\_tracing\_config) | Tracing mode of an Amazon SNS topic. Valid values: PassThrough, Active. | `string` | `null` | no |
| <a name="input_use_name_prefix"></a> [use\_name\_prefix](#input\_use\_name\_prefix) | Determines whether `name` is used as a prefix | `bool` | `false` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_subscriptions"></a> [subscriptions](#output\_subscriptions) | Map of subscriptions created and their attributes |
| <a name="output_topic_arn"></a> [topic\_arn](#output\_topic\_arn) | The ARN of the SNS topic, as a more obvious property (clone of id) |
| <a name="output_topic_beginning_archive_time"></a> [topic\_beginning\_archive\_time](#output\_topic\_beginning\_archive\_time) | The oldest timestamp at which a FIFO topic subscriber can start a replay |
| <a name="output_topic_id"></a> [topic\_id](#output\_topic\_id) | The ARN of the SNS topic |
| <a name="output_topic_name"></a> [topic\_name](#output\_topic\_name) | The name of the topic |
| <a name="output_topic_owner"></a> [topic\_owner](#output\_topic\_owner) | The AWS Account ID of the SNS topic owner |
<!-- END_TF_DOCS -->
