import boto3
import logging
import os
from datetime import datetime

# Configure logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

def lambda_handler(event, context):
    """
    Lambda function to start or stop all services in an ECS cluster.

    The function determines whether to start or stop services based on the 'action' parameter
    in the event. If not specified, it will determine the action based on the current time.

    Parameters:
    - event (dict): Lambda event data, can contain:
        - action (str, optional): 'start' or 'stop'. If not provided, will be determined by time.
        - cluster (str, optional): ECS cluster ARN. If not provided, uses environment variable or default.
    - context (LambdaContext): Lambda context object

    Returns:
    - dict: Result of the operation
    """
    # Get the ECS cluster ARN from event, environment variable, or use default
    cluster_arn = event.get('cluster',
                           os.environ.get('ECS_CLUSTER_ARN',
                                         'arn:aws-cn:ecs:cn-north-1:051953029949:cluster/msdpd-uatv-ecs-fargate'))

    # Determine action (start or stop) from event or based on time
    action = event.get('action')

    # If action not specified, determine based on time (example: stop after hours, start during work hours)
    if not action:
        current_hour = datetime.now().hour
        # Assuming work hours are 8 AM to 6 PM (8-18)
        if 8 <= current_hour < 18:
            action = 'start'
        else:
            action = 'stop'

    logger.info(f"Executing {action} action on cluster {cluster_arn}")

    # Initialize ECS client
    ecs_client = boto3.client('ecs', region_name='cn-north-1')

    try:
        # List all services in the cluster
        services = []
        next_token = None

        while True:
            if next_token:
                response = ecs_client.list_services(cluster=cluster_arn, nextToken=next_token)
            else:
                response = ecs_client.list_services(cluster=cluster_arn)

            services.extend(response['serviceArns'])

            if 'nextToken' in response:
                next_token = response['nextToken']
            else:
                break

        if not services:
            logger.info(f"No services found in cluster {cluster_arn}")
            return {
                'statusCode': 200,
                'body': f"No services found in cluster {cluster_arn}"
            }

        logger.info(f"Found {len(services)} services in cluster {cluster_arn}")

        # Process services in batches (ECS API limits to 10 services per call)
        batch_size = 10
        results = []

        for i in range(0, len(services), batch_size):
            batch = services[i:i + batch_size]

            # Get current service details
            service_details = ecs_client.describe_services(
                cluster=cluster_arn,
                services=batch
            )

            for service in service_details['services']:
                service_name = service['serviceName']
                current_count = service['desiredCount']

                if action == 'stop' and current_count > 0:
                    # Stop the service by setting desired count to 0
                    ecs_client.update_service(
                        cluster=cluster_arn,
                        service=service_name,
                        desiredCount=0
                    )
                    logger.info(f"Stopped service {service_name} (reduced from {current_count} to 0)")
                    results.append(f"Stopped {service_name}")

                elif action == 'start' and current_count == 0:
                    # Start the service by setting desired count to 1
                    # Note: This assumes the service should have at least 1 task
                    # For production, you might want to store the original count before stopping
                    ecs_client.update_service(
                        cluster=cluster_arn,
                        service=service_name,
                        desiredCount=1
                    )
                    logger.info(f"Started service {service_name} (increased from 0 to 1)")
                    results.append(f"Started {service_name}")

                else:
                    logger.info(f"Service {service_name} already in desired state (count: {current_count})")
                    results.append(f"No change for {service_name}")

        return {
            'statusCode': 200,
            'body': f"Successfully processed {len(services)} services. Results: {', '.join(results)}"
        }

    except Exception as e:
        logger.error(f"Error processing ECS services: {str(e)}")
        return {
            'statusCode': 500,
            'body': f"Error: {str(e)}"
        }