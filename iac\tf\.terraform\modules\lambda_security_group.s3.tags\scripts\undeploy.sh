#! /bin/bash

: """${TERRAFORM_IMAGE:=iac-shared.dock.merck.com/terraform/1.5.7:rel-2023w44.21}"""
: """${var:=""}"""
: """${ACTION:="deploy"}"""
: """${TF_DIR_PREFIX:="examples"}"""
: """${TFPLAN:="tfplan"}"""
: """${CD_PROJECT:="iac-shared"}"""
: """${AWS_CREDENTIALS:="aws_srv_deployment_user_tft2"}"""
: """${LOG_LEVEL:="INFO"}"""

set_environment(){
  BUILD_ENV=$(ectool getProperty --propertyName "/myEnvironment/envType")
  echo "BUILD_ENV=${BUILD_ENV}"
  echo "TERRAFORM_IMAGE=${TERRAFORM_IMAGE}"
  echo "ACTION=${ACTION}"
  echo "TF_DIR_PREFIX=${TF_DIR_PREFIX}"
  docker pull "${TERRAFORM_IMAGE}"

  if [[ "${LOG_LEVEL}" == "DEBUG" ]]; then
    set -x
  fi

  PW="$(pwd)"
  echo "Current path is: ${PW}. Content of the folder is:"
  ls -l

  echo "Custom Script $CS"
  echo "New var parameter for terraform plan: ${var}"

  # VARIABLES
  echo "[INFO] ACTION=${ACTION}"
  DEFAULT_GIT_DIR=$(ectool getProperty --propertyName "/myComponent/defaultGitDir")
  echo "DEFAULT_GIT_DIR=${DEFAULT_GIT_DIR}"

  DOCK_CONT_NAME="tf_deploy_$(date '+%Y%m%d')_$(echo $RANDOM | md5sum | head -c 4)"
  echo "DOCK_CONT_NAME=${DOCK_CONT_NAME}"
  HTTP_PROXY="http://usctcc.merck.com:8080"
  HTTPS_PROXY="http://usctcc.merck.com:8081"
  NO_PROXY="*.merck.com"
  # for CD
  WDIR="/tmp/workspace"
  WKDIR="/tmp/workspace/${DEFAULT_GIT_DIR}"
  GIT_CREDS_PATH="${WKDIR}/.creds"

  echo "cd ${DEFAULT_GIT_DIR}"
  cd ${DEFAULT_GIT_DIR}
 
}

set_folder_structure() {
  current_dir=$(pwd)
  run_dir="${current_dir}/${TF_DIR_PREFIX}"
  cd "${run_dir}"
  set +e
  subfolders=$(ls -d -- */ | sed 's/\/*$//g')
  set -e
  if [[ ${subfolders} == "" ]]; then
    subfolders="."
  fi
  echo "[INFO] Subfolders:"
  echo ${subfolders}
  cd "${current_dir}"
}

get_aws_credentials(){
  # Get credentials from project
  PIPELINE_NAME=$(basename -s .git `git config --get remote.origin.url`)
  echo "PIPELINE_NAME=${PIPELINE_NAME}"
  ectool attachCredential --projectName "iac-shared" --credentialName "${AWS_CREDENTIALS}" --applicationName "iac-shared-module-test" --processName "Deploy Application" --processStepName "Deploy component - CustomMC"
  AWS_ACCESS_KEY_ID=$(ectool getFullCredential "/projects/${CD_PROJECT}/credentials/${AWS_CREDENTIALS}" --value userName)
  AWS_SECRET_ACCESS_KEY=$(ectool getFullCredential "/projects/${CD_PROJECT}/credentials/${AWS_CREDENTIALS}" --value password)
}

run_docker() {
  ACTION=$1
  echo "Starting new docker container with image: ${TERRAFORM_IMAGE}..."
  echo "Docker container name is: ${DOCK_CONT_NAME}"
  echo "ACTION=${ACTION}"
  HTTP_PROXY="http://usctcc.merck.com:8080"
  HTTPS_PROXY="http://usctcc.merck.com:8081"
  NO_PROXY="*.merck.com"
  echo "[INFO] Starting terraform ${ACTION}..."
  set +e
  docker run --rm \
    --name="${DOCK_CONT_NAME}_${ACTION}" \
    --workdir="${WKDIR}/" \
    --env ACTION="${ACTION}" \
    --env AWS_ACCESS_KEY_ID="${AWS_ACCESS_KEY_ID}" \
    --env AWS_SECRET_ACCESS_KEY="${AWS_SECRET_ACCESS_KEY}" \
    --env BUILD_ENV="${BUILD_ENV}" \
    --env DEFAULT_GIT_DIR="${DEFAULT_GIT_DIR}" \
    --env DOCK_CONT_NAME="${DOCK_CONT_NAME}" \
    --env GIT_CREDS_PATH="${GIT_CREDS_PATH}" \
    --env HTTP_PROXY="${HTTP_PROXY}" \
    --env HTTPS_PROXY="${HTTPS_PROXY}"  \
    --env NO_PROXY="${NO_PROXY}"  \
    --env TF_DIR="${TF_DIR}" \
    --env TFPLAN="${TFPLAN}" \
    --env var="${var}" \
    --env WDIR="${WDIR}" \
    -v "$(pwd)":"/tmp/workspace/${DEFAULT_GIT_DIR}" \
    --entrypoint "/bin/bash" \
    ${TERRAFORM_IMAGE} \
    "scripts/run_terraform.sh"

  res=$?
  if [[ "${ACTION}" == "destroy" ]]; then
    ex_code_destroy=$(( ex_code_destroy + res ))
    echo "[INFO] ex_code_destroy=${ex_code_destroy}"
  fi

  set -e
}

######### MAIN SCRIPT
ex_code_destroy=0
set_environment
get_aws_credentials
set_folder_structure

for subfolder in ${subfolders}; do
  TF_DIR="${TF_DIR_PREFIX}/${subfolder}"
  echo ""
  echo "######################## ${TF_DIR} ########################"
  run_docker  "destroy"
done

if [[ "${ex_code_destroy}" != "0" ]]; then
  echo "[ERROR] One or more example destruction failed."
  exit ${ex_code_destroy}
fi
