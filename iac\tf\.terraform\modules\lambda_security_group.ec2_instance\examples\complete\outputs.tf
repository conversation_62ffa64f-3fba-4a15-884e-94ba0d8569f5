output "id" {
  description = "The ID of the instance"
  value       = try(module.ec2_instance.id, module.ec2_instance.spot_instance_id, null)
}

output "arn" {
  description = "The ARN of the instance"
  value       = try(module.ec2_instance.arn, null)
}

output "capacity_reservation_specification" {
  description = "Capacity reservation specification of the instance"
  value       = try(module.ec2_instance.capacity_reservation_specification, null)
}

output "instance_state" {
  description = "The state of the instance. One of: `pending`, `running`, `shutting-down`, `terminated`, `stopping`, `stopped`"
  value       = try(module.ec2_instance.instance_state, null)
}

output "outpost_arn" {
  description = "The ARN of the Outpost the instance is assigned to"
  value       = try(module.ec2_instance.outpost_arn, null)
}

output "password_data" {
  description = "Base-64 encoded encrypted password data for the instance. Useful for getting the administrator password for instances running Microsoft Windows. This attribute is only exported if `get_password_data` is true"
  value       = try(module.ec2_instance.password_data, null)
}

output "primary_network_interface_id" {
  description = "The ID of the instance's primary network interface"
  value       = try(module.ec2_instance.primary_network_interface_id, null)
}

output "private_dns" {
  description = "The private DNS name assigned to the instance. Can only be used inside the Amazon EC2, and only available if you've enabled DNS hostnames for your VPC"
  value       = try(module.ec2_instance.private_dns, null)
}

output "public_dns" {
  description = "The public DNS name assigned to the instance. For EC2-VPC, this is only available if you've enabled DNS hostnames for your VPC"
  value       = try(module.ec2_instance.public_dns, null)
}

output "public_ip" {
  description = "The public IP address assigned to the instance, if applicable. NOTE: If you are using an eip with your instance, you should refer to the EIP's address directly and not use `public_ip` as this field will change after the EIP is attached"
  value       = try(module.ec2_instance.public_ip, null)
}

output "private_ip" {
  description = "The private IP address assigned to the instance."
  value       = try(module.ec2_instance.private_ip, null)
}

output "ipv6_addresses" {
  description = "The IPv6 address assigned to the instance, if applicable."
  value       = try(module.ec2_instance.ipv6_addresses, [])
}

output "tags_all" {
  description = "A map of tags assigned to the resource, including those inherited from the provider default_tags configuration block"
  value       = try(module.ec2_instance.tags_all, {})
}

output "spot_bid_status" {
  description = "The current bid status of the Spot Instance Request"
  value       = try(module.ec2_instance.spot_bid_status, null)
}

output "spot_state" {
  description = "The current request state of the Spot Instance Request"
  value       = try(module.ec2_instance.spot_state, null)
}

output "ami" {
  description = "AMI ID that was used to create the instance"
  value       = try(module.ec2_instance.ami, null)
}

output "availability_zone" {
  description = "The availability zone of the created instance"
  value       = try(module.ec2_instance.availability_zone, null)
}

################################################################################
# IAM Role / Instance Profile
################################################################################

output "iam_role_name" {
  description = "The name of the IAM role"
  value       = try(module.ec2_instance.iam_role_name, null)
}

output "iam_role_arn" {
  description = "The Amazon Resource Name (ARN) specifying the IAM role"
  value       = try(module.ec2_instance.iam_role_arn, null)
}

output "iam_role_unique_id" {
  description = "Stable and unique string identifying the IAM role"
  value       = try(module.ec2_instance.iam_role_unique_id, null)
}

output "iam_instance_profile_arn" {
  description = "ARN assigned by AWS to the instance profile"
  value       = try(module.ec2_instance.iam_instance_profile_arn, null)
}

output "iam_instance_profile_id" {
  description = "Instance profile's ID"
  value       = try(module.ec2_instance.iam_instance_profile_id, null)
}

output "iam_instance_profile_unique" {
  description = "Stable and unique string identifying the IAM instance profile"
  value       = try(module.ec2_instance.iam_instance_profile_unique, null)
}
################################################################################
# Block Devices
################################################################################
output "root_block_device" {
  description = "Root block device information"
  value       = try(module.ec2_instance.root_block_device, null)
}

output "ebs_block_device" {
  description = "EBS block device information"
  value       = try(module.ec2_instance.ebs_block_device, null)
}

output "ephemeral_block_device" {
  description = "Ephemeral block device information"
  value       = try(module.ec2_instance.ephemeral_block_device, null)
}
################################################################################
# Cloudwatch Log Group
################################################################################
output "cloudwatch_log_group_name" {
  description = "Name of Cloudwatch log group"
  value       = module.cloudwatch_log_group.cloudwatch_log_group_name
}

output "cloudwatch_log_group_arn" {
  description = "ARN of Cloudwatch log group"
  value       = module.cloudwatch_log_group.cloudwatch_log_group_arn
}
