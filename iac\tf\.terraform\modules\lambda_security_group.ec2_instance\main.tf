data "aws_region" "current" {}

#>>GENERATE_CONTENT
module "ec2_instance_wrapper" {
  source  = "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/ec2-instance/aws"
  version = "5.7.1"

  ami                                  = var.ami
  ami_ssm_parameter                    = var.ami_ssm_parameter
  associate_public_ip_address          = false
  availability_zone                    = var.availability_zone
  capacity_reservation_specification   = var.capacity_reservation_specification
  cpu_core_count                       = var.cpu_core_count
  cpu_credits                          = var.cpu_credits
  cpu_options                          = var.cpu_options
  cpu_threads_per_core                 = var.cpu_threads_per_core
  create                               = var.create
  create_eip                           = var.create_eip
  create_iam_instance_profile          = var.create_iam_instance_profile
  create_spot_instance                 = var.create_spot_instance
  disable_api_stop                     = var.disable_api_stop
  disable_api_termination              = var.disable_api_termination
  ebs_block_device                     = var.enable_ebs_creation ? local.encrypted_ebs_volume : []
  ebs_optimized                        = var.ebs_optimized
  eip_domain                           = var.eip_domain
  eip_tags                             = var.eip_tags
  enable_volume_tags                   = var.enable_volume_tags
  enclave_options_enabled              = var.enclave_options_enabled
  ephemeral_block_device               = var.ephemeral_block_device
  get_password_data                    = var.get_password_data
  hibernation                          = var.hibernation
  host_id                              = var.host_id
  iam_instance_profile                 = var.iam_instance_profile
  iam_role_description                 = var.iam_role_description
  iam_role_name                        = var.iam_role_name
  iam_role_path                        = var.iam_role_path
  iam_role_permissions_boundary        = var.iam_role_permissions_boundary
  iam_role_policies                    = var.iam_role_policies
  iam_role_tags                        = var.iam_role_tags
  iam_role_use_name_prefix             = var.iam_role_use_name_prefix
  ignore_ami_changes                   = var.ignore_ami_changes
  instance_initiated_shutdown_behavior = var.instance_initiated_shutdown_behavior
  instance_tags                        = var.instance_tags
  instance_type                        = var.instance_type
  ipv6_address_count                   = var.ipv6_address_count
  ipv6_addresses                       = var.ipv6_addresses
  key_name                             = var.key_name
  launch_template                      = var.launch_template
  maintenance_options                  = var.maintenance_options
  metadata_options = {
    http_endpoint               = var.http_endpoint
    http_tokens                 = "required"
    http_put_response_hop_limit = 1
    instance_metadata_tags      = "enabled"
  }
  monitoring               = true
  name                     = var.name
  network_interface        = var.network_interface
  placement_group          = var.placement_group
  private_dns_name_options = var.private_dns_name_options
  private_ip               = var.private_ip
  root_block_device = [
    {
      encrypted             = true
      delete_on_termination = var.root_block_device_deletion_on_termination
      iops                  = var.root_block_device_iops
      kms_key_id            = var.root_block_device_kms
      volume_type           = var.root_block_device_volume_type
      throughput            = var.root_block_device_throughput
      volume_size           = var.root_block_device_volume_size
      tags                  = var.root_block_tags
    },
  ]
  secondary_private_ips               = var.secondary_private_ips
  source_dest_check                   = var.source_dest_check
  spot_block_duration_minutes         = var.spot_block_duration_minutes
  spot_instance_interruption_behavior = var.spot_instance_interruption_behavior
  spot_launch_group                   = var.spot_launch_group
  spot_price                          = var.spot_price
  spot_type                           = var.spot_type
  spot_valid_from                     = var.spot_valid_from
  spot_valid_until                    = var.spot_valid_until
  spot_wait_for_fulfillment           = var.spot_wait_for_fulfillment
  subnet_id                           = var.subnet_id
  tags                                = merge(local.core_infra_tags, module.tags.tags)
  tenancy                             = var.tenancy
  timeouts                            = var.timeouts
  user_data                           = var.user_data
  user_data_base64                    = var.user_data_base64
  user_data_replace_on_change         = var.user_data_replace_on_change
  volume_tags                         = var.volume_tags
  vpc_security_group_ids              = var.vpc_security_group_ids
}

#>>GENERATE_CONTENT

############### Optional Opt-in: Core Infra self-service Backup Product ###############
# More info: https://share.merck.com/display/INFRA/CoreInfra-Backup

locals {
  encrypted_ebs_volume = [for i in var.ebs_volumes : merge(i, local.encrypt)] #Local block to map value of encrytion as true to all EBS Volumes
  encrypt = {
    encrypted = true
  }
  value = merge(var.tags, { Name = var.name })
  core_infra_tags = {
    "CoreInfraTag-Backup"             = var.enable_coreinfra_backup ? "Yes" : "No"
    "CoreInfraTag-BackupCopyToBunker" = var.enable_coreinfra_backup_copy_to_bunker ? "Yes" : "No"
  }
}

resource "local_file" "wait_for_instance" {
  count = try(var.wait_for_instance.enabled, false) ? 1 : 0

  content = templatefile(
    var.wait_for_instance.custom_script == null ? "${path.module}/wait.sh.tpl" : var.wait_for_instance.custom_script,
    {
      INSTANCE_ID         = try(module.ec2_instance_wrapper.id, module.ec2_instance_wrapper.spot_instance_id, null)
      TAG_NAME            = var.wait_for_instance.tag_name
      SLEEP_TIME          = var.wait_for_instance.sleep_time
      NUM_OF_CYCLES       = var.wait_for_instance.num_of_cycles
      FAILED_VALUE        = var.wait_for_instance.failed_value
      DEPLOYMENT_ROLE_ARN = var.wait_for_instance.deployment_role_arn
      REGION              = var.wait_for_instance.region == null ? data.aws_region.current.name : var.wait_for_instance.region
    }
  )
  filename = ".terraform/wait.sh"
}

resource "null_resource" "wait_for_instance" {
  count = try(var.wait_for_instance.enabled, false) ? 1 : 0
  depends_on = [
    module.ec2_instance_wrapper
  ]

  triggers = {
    instance_id = try(module.ec2_instance_wrapper.id, module.ec2_instance_wrapper.spot_instance_id, null)
  }
  provisioner "local-exec" {
    interpreter = var.wait_for_instance.interpreter
    command     = "./${local_file.wait_for_instance[0].filename}"
  }
}
