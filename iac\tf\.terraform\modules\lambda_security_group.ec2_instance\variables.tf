#>>GENERATE_CONTENT

variable "default_tags" {
  type        = map(string)
  description = "A map of default tags to be applied on each resource. You can get required tags [here](https://cloud.merck.com/documentation/compliance/tagging-standards/index.html)"
  validation {
    condition     = length(setintersection(keys(var.default_tags), ["DataClassification", "Consumer", "Application", "Environment", "Service"])) >= length(["DataClassification", "Consumer", "Application", "Environment", "Service"])
    error_message = "Keys: DataClassification, Consumer, Application, Environment, Service are required!"
  }
}

variable "ami" {
  type        = string
  default     = null
  description = "ID of AMI to use for the instance"
}

variable "ami_ssm_parameter" {
  type        = string
  default     = "/aws/service/ami-amazon-linux-latest/amzn2-ami-hvm-x86_64-gp2"
  description = "SSM parameter name for the AMI ID. For Amazon Linux AMI SSM parameters see [reference](https://docs.aws.amazon.com/systems-manager/latest/userguide/parameter-store-public-parameters-ami.html)"
}

variable "availability_zone" {
  type        = string
  default     = null
  description = "AZ to start the instance in"
}

variable "capacity_reservation_specification" {
  type        = any
  default     = {}
  description = "Describes an instance's Capacity Reservation targeting option"
}

variable "cpu_core_count" {
  type        = number
  default     = null
  description = "Sets the number of CPU cores for an instance"
}

variable "cpu_credits" {
  type        = string
  default     = null
  description = "The credit option for CPU usage (unlimited or standard)"
}

variable "cpu_options" {
  type        = any
  default     = {}
  description = "Defines CPU options to apply to the instance at launch time."
}

variable "cpu_threads_per_core" {
  type        = number
  default     = null
  description = "Sets the number of CPU threads per core for an instance (has no effect unless cpu_core_count is also set)"
}

variable "create" {
  type        = bool
  default     = true
  description = "Whether to create an instance"
}

variable "create_eip" {
  type        = bool
  default     = false
  description = "Determines whether a public EIP will be created and associated with the instance."
}

variable "create_iam_instance_profile" {
  type        = bool
  default     = true
  description = "Determines whether an IAM instance profile is created or to use an existing IAM instance profile"
}

variable "create_spot_instance" {
  type        = bool
  default     = false
  description = "Depicts if the instance is a spot instance"
}

variable "disable_api_stop" {
  type        = bool
  default     = null
  description = "If true, enables EC2 Instance Stop Protection"
}

variable "disable_api_termination" {
  type        = bool
  default     = null
  description = "If true, enables EC2 Instance Termination Protection"
}

variable "ebs_optimized" {
  type        = bool
  default     = true
  description = "If true, the launched EC2 instance will be EBS-optimized"
}

variable "ebs_volumes" {
  type = list(object({
    device_name           = string
    volume_size           = optional(string)
    volume_type           = optional(string)
    kms_key_id            = optional(string)
    throughput            = optional(number)
    delete_on_termination = optional(bool)
    iops                  = optional(number)
    snapshot_id           = optional(string)
    tags                  = optional(map(string))
  }))
  default = [
    {
      "device_name" : "/dev/sdf",
      "kms_key_id" : null,
      "throughput" : 125,
      "volume_size" : 5,
      "volume_type" : "gp3"
    }
  ]
  description = <<-DESCRIPTION
    Configuration parameters for EBS block device/s
    ```
    list(object({
      device_name           = string, # Name of EBS device
      volume_size           = optional(number),     # Size of additional EBS block devices to attach to the instance
      volume_type           = optional(string),     # Type of additional EBS block devices to attach to the instance
      kms_key_id            = optional(string),     # EBS block devices to attach to the instance
      throughput            = optional(number),     # Additional EBS block devices to attach to the instance
      delete_on_termination = optional(bool),       # Whether the volume should be destroyed on instance termination. Defaults to true.
      iops                  = optional(number),     # Amount of provisioned IOPS. Only valid for volume_type of io1, io2 or gp3.
      snapshot_id           = optional(string),     # Snapshot ID to mount.
      volume_size           = optional(string),     # Size of the volume in gibibytes (GiB).
      volume_type           = optional(string),     # Type of volume. Valid values include standard, gp2, gp3, io1, io2, sc1, or st1. Defaults to gp2.
      tags                  = optional(map(string)) # Map of tags to assign to the device.
    }))
    ```
  DESCRIPTION
}

variable "eip_domain" {
  type        = string
  default     = "vpc"
  description = "Indicates if this EIP is for use in VPC"
}

variable "eip_tags" {
  type        = map(string)
  default     = {}
  description = "A map of additional tags to add to the eip"
}

variable "enable_coreinfra_backup" {
  type        = bool
  default     = true
  description = "Whether to enable default CoreInfra-backup"
}

variable "enable_coreinfra_backup_copy_to_bunker" {
  type        = bool
  default     = false
  description = "Whether to enable default CoreInfra-backup-copy-to-bunker"
}

variable "enable_ebs_creation" {
  type        = bool
  default     = true
  description = "Whether to create an EBS block device"
}

variable "enable_volume_tags" {
  type        = bool
  default     = true
  description = "Whether to enable volume tags (if enabled it conflicts with root_block_device tags)"
}

variable "enclave_options_enabled" {
  type        = bool
  default     = null
  description = "Whether Nitro Enclaves will be enabled on the instance. Defaults to `false`"
}

variable "ephemeral_block_device" {
  type        = list(map(string))
  default     = []
  description = "Customize Ephemeral (also known as Instance Store) volumes on the instance"
}

variable "get_password_data" {
  type        = bool
  default     = null
  description = "If true, wait for password data to become available and retrieve it"
}

variable "hibernation" {
  type        = bool
  default     = null
  description = "If true, the launched EC2 instance will support hibernation"
}

variable "host_id" {
  type        = string
  default     = null
  description = "ID of a dedicated host that the instance will be assigned to. Use when an instance is to be launched on a specific dedicated host"
}

variable "http_endpoint" {
  type        = string
  default     = "enabled"
  description = "Customize the metadata options of the instance"
}

variable "iam_instance_profile" {
  type        = string
  description = "IAM Instance Profile to launch the instance with. Specified as the name of the Instance Profile"
}

variable "iam_role_description" {
  type        = string
  default     = null
  description = "Description of the role"
}

variable "iam_role_name" {
  type        = string
  default     = null
  description = "Name to use on IAM role created"
}

variable "iam_role_path" {
  type        = string
  default     = null
  description = "IAM role path"
}

variable "iam_role_permissions_boundary" {
  type        = string
  default     = null
  description = "ARN of the policy that is used to set the permissions boundary for the IAM role"
}

variable "iam_role_policies" {
  type        = map(string)
  default     = {}
  description = "Policies attached to the IAM role"
}

variable "iam_role_tags" {
  type        = map(string)
  default     = {}
  description = "A map of additional tags to add to the IAM role/profile created"
}

variable "iam_role_use_name_prefix" {
  type        = bool
  default     = true
  description = "Determines whether the IAM role name (`iam_role_name` or `name`) is used as a prefix"
}

variable "ignore_ami_changes" {
  type        = bool
  default     = false
  description = "Whether changes to the AMI ID changes should be ignored by Terraform. Note - changing this value will result in the replacement of the instance"
}

variable "instance_initiated_shutdown_behavior" {
  type        = string
  default     = null
  description = "Shutdown behavior for the instance. Amazon defaults this to stop for EBS-backed instances and terminate for instance-store instances. Cannot be set on instance-store instance"
}

variable "instance_tags" {
  type        = map(string)
  default     = {}
  description = "Additional tags for the instance"
}

variable "instance_type" {
  type        = string
  default     = "t3a.micro"
  description = "The type of instance to start"
}

variable "ipv6_address_count" {
  type        = number
  default     = null
  description = "A number of IPv6 addresses to associate with the primary network interface. Amazon EC2 chooses the IPv6 addresses from the range of your subnet"
}

variable "ipv6_addresses" {
  type        = list(string)
  default     = null
  description = "Specify one or more IPv6 addresses from the range of the subnet to associate with the primary network interface"
}

variable "key_name" {
  type        = string
  default     = null
  description = "Key name of the Key Pair to use for the instance; which can be managed using the `aws_key_pair` resource"
}

variable "launch_template" {
  type        = map(string)
  default     = {}
  description = "Specifies a Launch Template to configure the instance. Parameters configured on this resource will override the corresponding parameters in the Launch Template"
}

variable "maintenance_options" {
  type        = any
  default     = {}
  description = "The maintenance options for the instance"
}

variable "name" {
  type        = string
  default     = ""
  description = "Name to be used on EC2 instance created"
}

variable "network_interface" {
  type        = list(map(string))
  default     = []
  description = "Customize network interfaces to be attached at instance boot time"
}

variable "placement_group" {
  type        = string
  default     = null
  description = "The Placement Group to start the instance in"
}

variable "private_dns_name_options" {
  type        = map(string)
  default     = {}
  description = "Customize the private DNS name options of the instance"
}

variable "private_ip" {
  type        = string
  default     = null
  description = "Private IP address to associate with the instance in a VPC"
}

variable "root_block_device_deletion_on_termination" {
  type        = bool
  default     = true
  description = "Whether the volume should be destroyed on instance termination. Defaults to true."
}

variable "root_block_device_iops" {
  type        = number
  default     = null
  description = "Amount of provisioned IOPS. Only valid for volume_type of io1, io2 or gp3.."
}

variable "root_block_device_kms" {
  type        = string
  default     = null
  description = "Amazon Resource Name (ARN) of the KMS Key to use when encrypting the volume. Must be configured to perform drift detection."
}

variable "root_block_device_throughput" {
  type        = string
  default     = 200
  description = "Customize details about the root block device of the instance."
}

variable "root_block_device_volume_size" {
  type        = string
  default     = 50
  description = "Customize details about the root block device of the instance."
}

variable "root_block_device_volume_type" {
  type        = string
  default     = "gp3"
  description = "Customize details about the root block device of the instance."
}

variable "root_block_tags" {
  type        = map(string)
  default     = {}
  description = "A mapping of tags to assign to the resource"
}

variable "secondary_private_ips" {
  type        = list(string)
  default     = null
  description = "A list of secondary private IPv4 addresses to assign to the instance's primary network interface (eth0) in a VPC. Can only be assigned to the primary network interface (eth0) attached at instance creation, not a pre-existing network interface i.e. referenced in a `network_interface block`"
}

variable "source_dest_check" {
  type        = bool
  default     = true
  description = "Controls if traffic is routed to the instance when the destination address does not match the instance. Used for NAT or VPNs"
}

variable "spot_block_duration_minutes" {
  type        = number
  default     = null
  description = "The required duration for the Spot instances, in minutes. This value must be a multiple of 60 (60, 120, 180, 240, 300, or 360)"
}

variable "spot_instance_interruption_behavior" {
  type        = string
  default     = null
  description = "Indicates Spot instance behavior when it is interrupted. Valid values are `terminate`, `stop`, or `hibernate`"
}

variable "spot_launch_group" {
  type        = string
  default     = null
  description = "A launch group is a group of spot instances that launch together and terminate together. If left empty instances are launched and terminated individually"
}

variable "spot_price" {
  type        = string
  default     = null
  description = "The maximum price to request on the spot market. Defaults to on-demand price"
}

variable "spot_type" {
  type        = string
  default     = null
  description = "If set to one-time, after the instance is terminated, the spot request will be closed. Default `persistent`"
}

variable "spot_valid_from" {
  type        = string
  default     = null
  description = "The start date and time of the request, in UTC RFC3339 format(for example, YYYY-MM-DDTHH:MM:SSZ)"
}

variable "spot_valid_until" {
  type        = string
  default     = null
  description = "The end date and time of the request, in UTC RFC3339 format(for example, YYYY-MM-DDTHH:MM:SSZ)"
}

variable "spot_wait_for_fulfillment" {
  type        = bool
  default     = null
  description = "If set, Terraform will wait for the Spot Request to be fulfilled, and will throw an error if the timeout of 10m is reached"
}

variable "subnet_id" {
  type        = string
  default     = null
  description = "The VPC Subnet ID to launch in"
}

variable "tags" {
  type        = map(string)
  default     = {}
  description = "A mapping of tags to assign to the resource"
}

variable "tenancy" {
  type        = string
  default     = null
  description = "The tenancy of the instance (if the instance is running in a VPC). Available values: default, dedicated, host"
}

variable "timeouts" {
  type        = map(string)
  default     = {}
  description = "Define maximum timeout for creating, updating, and deleting EC2 instance resources"
}

variable "user_data" {
  type        = string
  default     = null
  description = "The user data to provide when launching the instance. Do not pass gzip-compressed data via this argument; see user_data_base64 instead"
}

variable "user_data_base64" {
  type        = string
  default     = null
  description = "Can be used instead of user_data to pass base64-encoded binary data directly. Use this instead of user_data whenever the value is not a valid UTF-8 string. For example, gzip-encoded user data must be base64-encoded and passed via this argument to avoid corruption"
}

variable "user_data_replace_on_change" {
  type        = bool
  default     = false
  description = "When used in combination with user_data or user_data_base64 will trigger a destroy and recreate when set to true. Defaults to false if not set"
}

variable "volume_tags" {
  type        = map(string)
  default     = {}
  description = "A mapping of tags to assign to the devices created by the instance at launch time"
}

variable "vpc_security_group_ids" {
  type        = list(string)
  default     = null
  description = "A list of security group IDs to associate with"
}

variable "wait_for_instance" {
  type = object({
    enabled       = optional(bool, false)
    custom_script = optional(string)
    sleep_time    = optional(number, 10)
    num_of_cycles = optional(number, 1000)
    region        = optional(string)
    interpreter = optional(list(string), [
      "bash",
      "-c"
    ])
    failed_value        = string
    tag_name            = string
    deployment_role_arn = string
  })
  default = {
    "enabled" : false,
    "failed_value" : "failed",
    "tag_name" : "initialization",
    "deployment_role_arn" : ""
  }
  description = <<-DESCRIPTION
    Use this variable to configure [wait.sh.tpl](wait.sh.tpl) script. If you want to use custom script, please know that
    INSTANCE_ID, SLEEP_TIME, NUM_OF_CYCLES, FAILED_VALUE, TAG_NAME and DEPLOYMENT_ROLE_ARN are template variables and are going to be replaced in your script.
    ```terraform
    type = object({
      enabled             (Optional) whether the null_resource for waiting should be created
      custom_script       (Optional) path to template file which will be used to wait for ec2 instance to finish the user data, if not specified, bash script in this module is going to be used
      sleep_time          (Optional) for how long should we sleep between checking for tag
      num_of_cycles       (Optional) how many cycles of wait are going to happen before we give up
      region              (Optional) region where the ec2 instance is deployed to, if nothing is passed, output from data source is going to be used
      interpreter         (Optional) which interpreter is going to be used, defaults to bash
      failed_value        value which we set as a failure
      tag_name            name of a tag for which we will wait
      deployment_role_arn role that has ec2:DescribeTags permission
    })
    ```
  DESCRIPTION
}
#>>GENERATE_CONTENT
