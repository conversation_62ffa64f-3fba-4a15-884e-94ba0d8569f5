######################################################################################
# Required "User Provided" Variables
# you must modify these with application specific information
######################################################################################

# Labels - this will create consistent naming and tagging across all resources
# All labels become part of the AWS resource name based on label_order
resource_vars = {
  appname     = "tft"
  region      = "us-east-1"
  attributes  = ["wrap", "ec2", "win"]
  label_order = ["appname", "region"]
  environment = "dev"
}

# Tags - the following tags will be added across all resources
default_tags = {
  Application        = "tft"
  Consumer           = "<EMAIL>"
  Environment        = "Development"
  DataClassification = "Proprietary"
  Service            = "TFT-Development"
}

# AWS Account Information
account_no      = "************"
deployment_role = "ec2-instance-wrapper"
region          = "us-east-1"
vpc_id          = "vpc-07a6c7e059c0195e9"
subnet_id       = "subnet-0668a69cb1a984567"

######################################################################################
# Optional "User Provided" Variables
# you can modify the values below to suit your application as needed
######################################################################################

instance_type        = "t3a.large"
availability_zone    = "us-east-1b"
key_pair_name        = "iac-shared"
iam_instance_profile = "myapp"

user_data_replace_on_change = true

enable_volume_tags = false #conflicts with root_block_device

root_block_device_volume_size = 200
