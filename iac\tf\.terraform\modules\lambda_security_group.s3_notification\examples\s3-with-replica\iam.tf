resource "random_pet" "this" {
  length = 2
}

locals {
  kms_key_arn         = (var.kms_source_key_id != "" ? var.kms_source_key_id : module.aws_kms_key.key_arn)
  kms_key_arn_replica = (var.kms_replica_key_id != "" ? var.kms_replica_key_id : module.aws_kms_key_replica.key_arn)
}

resource "aws_iam_role" "replication" {
  name = "s3-bucket-replication-${random_pet.this.id}"

  assume_role_policy = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": "sts:AssumeRole",
      "Principal": {
        "Service": "s3.amazonaws.com"
      },
      "Effect": "Allow",
      "Sid": ""
    }
  ]
}
POLICY
}

resource "aws_iam_policy" "replication" {
  name = "s3-bucket-replication-${random_pet.this.id}"

  policy = <<POLICY
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": [
        "s3:ListBucket",
        "s3:GetReplicationConfiguration",
        "s3:GetObjectVersionForReplication",
        "s3:GetObjectVersionAcl",
        "s3:GetObjectVersionTagging"
      ],
      "Effect": "Allow",
      "Resource": [
        "arn:${data.aws_partition.current.partition}:s3:::${module.labels.id}",
        "arn:${data.aws_partition.current.partition}:s3:::${module.labels.id}/*"
      ]
    },
    {
      "Action": [
        "s3:ReplicateObject",
        "s3:ReplicateDelete",
        "s3:ReplicateTags"
      ],
      "Effect": "Allow",
      "Resource": "arn:${data.aws_partition.current.partition}:s3:::${local.replica_bucket_name}/*",
      "Condition":{
        "StringLikeIfExists":{
            "s3:x-amz-server-side-encryption":[
              "aws:kms",
              "AES256",
              "aws:kms:dsse"
            ],
            "s3:x-amz-server-side-encryption-aws-kms-key-id":[
              "${local.kms_key_arn_replica}"
            ]
        }
      }
    },
    {
        "Action":[
          "kms:Decrypt"
        ],
        "Effect":"Allow",
        "Condition":{
          "StringLike":{
              "kms:ViaService":"s3.${var.region}.amazonaws.com",
              "kms:EncryptionContext:${data.aws_partition.current.partition}:s3:arn":[
                "arn:${data.aws_partition.current.partition}:s3:::${module.labels.id}/*"
              ]
          }
        },
        "Resource":[
          "${local.kms_key_arn}"
        ]
    },
    {
        "Action":[
          "kms:Encrypt"
        ],
        "Effect":"Allow",
        "Condition":{
          "StringLike":{
              "kms:ViaService":"s3.${var.replica_region}.amazonaws.com",
              "kms:EncryptionContext:${data.aws_partition.current.partition}:s3:arn":[
                "arn:${data.aws_partition.current.partition}:s3:::${local.replica_bucket_name}/*"
              ]
          }
        },
        "Resource":[
          "${local.kms_key_arn_replica}"
        ]
    }
  ]
}
POLICY
}

resource "aws_iam_policy_attachment" "replication" {
  name       = "s3-bucket-replication-${random_pet.this.id}"
  roles      = [aws_iam_role.replication.name]
  policy_arn = aws_iam_policy.replication.arn
}
