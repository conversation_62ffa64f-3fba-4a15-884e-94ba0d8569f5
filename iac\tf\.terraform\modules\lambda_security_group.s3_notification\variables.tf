#>>GENERATE_CONTENT

variable "default_tags" {
  type        = map(string)
  description = "A map of default tags to be applied on each resource. You can get required tags [here](https://cloud.merck.com/documentation/compliance/tagging-standards/index.html)"
  validation {
    condition     = length(setintersection(keys(var.default_tags), ["DataClassification", "Consumer", "Application", "Environment", "Service"])) >= length(["DataClassification", "Consumer", "Application", "Environment", "Service"])
    error_message = "Keys: DataClassification, Consumer, Application, Environment, Service are required!"
  }
}

variable "acceleration_status" {
  type        = string
  default     = null
  description = "(Optional) Sets the accelerate configuration of an existing bucket. Can be Enabled or Suspended."
}

variable "access_log_delivery_policy_source_accounts" {
  type        = list(string)
  default     = []
  description = "(Optional) List of AWS Account IDs should be allowed to deliver access logs to this bucket."
}

variable "access_log_delivery_policy_source_buckets" {
  type        = list(string)
  default     = []
  description = "(Optional) List of S3 bucket ARNs which should be allowed to deliver access logs to this bucket."
}

variable "acl" {
  type        = string
  default     = null
  description = "(Optional) The canned ACL to apply. Conflicts with `grant`"
}

variable "allowed_kms_key_arn" {
  type        = string
  default     = null
  description = "The ARN of KMS key which should be allowed in PutObject"
}

variable "analytics_configuration" {
  type        = any
  default     = {}
  description = "Map containing bucket analytics configuration."
}

variable "analytics_self_source_destination" {
  type        = bool
  default     = false
  description = "Whether or not the analytics source bucket is also the destination bucket."
}

variable "analytics_source_account_id" {
  type        = string
  default     = null
  description = "The analytics source account id."
}

variable "analytics_source_bucket_arn" {
  type        = string
  default     = null
  description = "The analytics source bucket ARN."
}

variable "attach_access_log_delivery_policy" {
  type        = bool
  default     = false
  description = "Controls if S3 bucket should have S3 access log delivery policy attached"
}

variable "attach_analytics_destination_policy" {
  type        = bool
  default     = false
  description = "Controls if S3 bucket should have bucket analytics destination policy attached."
}

variable "attach_deny_incorrect_encryption_headers" {
  type        = bool
  default     = false
  description = "Controls if S3 bucket should deny incorrect encryption headers policy attached."
}

variable "attach_deny_incorrect_kms_key_sse" {
  type        = bool
  default     = false
  description = "Controls if S3 bucket policy should deny usage of incorrect KMS key SSE."
}

variable "attach_deny_unencrypted_object_uploads" {
  type        = bool
  default     = false
  description = "Controls if S3 bucket should deny unencrypted object uploads policy attached."
}

variable "attach_elb_log_delivery_policy" {
  type        = bool
  default     = false
  description = "Controls if S3 bucket should have ELB log delivery policy attached"
}

variable "attach_inventory_destination_policy" {
  type        = bool
  default     = false
  description = "Controls if S3 bucket should have bucket inventory destination policy attached."
}

variable "attach_lb_log_delivery_policy" {
  type        = bool
  default     = false
  description = "Controls if S3 bucket should have ALB/NLB log delivery policy attached"
}

variable "attach_policy" {
  type        = bool
  default     = false
  description = "Controls if S3 bucket should have bucket policy attached (set to `true` to use value of `policy` as bucket policy)"
}

variable "attach_public_policy" {
  type        = bool
  default     = true
  description = "Controls if a user defined public bucket policy will be attached (set to `false` to allow upstream to apply defaults to the bucket)"
}

variable "bucket" {
  type        = string
  default     = null
  description = "(Optional, Forces new resource) The name of the bucket. If omitted, Terraform will assign a random, unique name."
}

variable "bucket_key_enabled" {
  type        = bool
  default     = null
  description = "Whether or not to use [Amazon S3 Bucket Keys for SSE-KMS](https://docs.aws.amazon.com/AmazonS3/latest/userguide/bucket-key.html)."
}

variable "bucket_prefix" {
  type        = string
  default     = null
  description = "(Optional, Forces new resource) Creates a unique bucket name beginning with the specified prefix. Conflicts with bucket."
}

variable "control_object_ownership" {
  type        = bool
  default     = false
  description = "Whether to manage S3 Bucket Ownership Controls on this bucket."
}

variable "cors_rule" {
  type        = any
  default     = []
  description = "List of maps containing rules for Cross-Origin Resource Sharing."
}

variable "create_bucket" {
  type        = bool
  default     = true
  description = "Controls if S3 bucket should be created"
}

variable "expected_bucket_owner" {
  type        = string
  default     = null
  description = "The account ID of the expected bucket owner"
}

variable "force_destroy" {
  type        = bool
  default     = false
  description = "(Optional, Default:false ) A boolean that indicates all objects should be deleted from the bucket so that the bucket can be destroyed without error. These objects are not recoverable."
}

variable "grant" {
  type        = any
  default     = []
  description = "An ACL policy grant. Conflicts with `acl`"
}

variable "intelligent_tiering" {
  type        = any
  default     = {}
  description = "Map containing intelligent tiering configuration."
}

variable "inventory_configuration" {
  type        = any
  default     = {}
  description = "Map containing S3 inventory configuration."
}

variable "inventory_self_source_destination" {
  type        = bool
  default     = false
  description = "Whether or not the inventory source bucket is also the destination bucket."
}

variable "inventory_source_account_id" {
  type        = string
  default     = null
  description = "The inventory source account id."
}

variable "inventory_source_bucket_arn" {
  type        = string
  default     = null
  description = "The inventory source bucket ARN."
}

variable "kms_key_id" {
  type        = string
  default     = null
  description = "KMS Key for Replication"
}

variable "lifecycle_rule" {
  type        = any
  default     = []
  description = "List of maps containing configuration of object lifecycle management."
}

variable "logging" {
  type = object({
    create        = optional(bool, true)
    target_bucket = optional(string)
    target_prefix = optional(string, "log/")
    target_object_key_format = optional(object({
      partitioned_prefix = optional(map(string))
      simple_prefix      = optional(bool)
    }))
  })
  default = {
    "create" : true,
    "target_prefix" : "log/",
    "target_object_key_format" : {
      "simple_prefix" : true
    }
  }
  description = <<-DESCRIPTION
    Map containing access bucket logging configuration.
    
    Conditions for the logging configuration:
      - If logging must be disabled, then create should be set to false, and target_bucket should be null.
      - If we want to use a log bucket that already exists, then create should be set to false, and target_bucket should be set to "existing_bucket_name".
      - If logging must be enabled, then create should be set to true
    
    ```
    type = object({
      create        = bool
      target_bucket = string
      target_prefix = string
      target_object_key_format = optional(object({
        partitioned_prefix = optional(object({
          partition_date_source = optional(map(string))   # Specifies the partition date source for the partitioned prefix. Valid values: EventTime, DeliveryTime.
          simple_prefix = optional(bool)                   
        }))
    })
    ```
  DESCRIPTION
  validation {
    condition     = !(var.logging.create && var.logging.target_bucket != null)
    error_message = "Only logging.create can be true or logging.target_bucket can be set."
  }
}

variable "metric_configuration" {
  type        = any
  default     = []
  description = "Map containing bucket metric configuration."
}

variable "object_lock_configuration" {
  type        = any
  default     = {}
  description = "Map containing S3 object locking configuration."
}

variable "object_lock_enabled" {
  type        = bool
  default     = false
  description = "Whether S3 bucket should have an Object Lock configuration enabled."
}

variable "owner" {
  type        = map(string)
  default     = {}
  description = "Bucket owner's display name and ID. Conflicts with `acl`"
}

variable "policy" {
  type        = string
  default     = null
  description = "(Optional) A valid bucket policy JSON document. Note that if the policy document is not specific enough (but still valid), Terraform may view the policy as constantly changing in a terraform plan. In this case, please make sure you use the verbose/specific version of the policy. For more information about building AWS IAM policy documents with Terraform, see the AWS IAM Policy Document Guide."
}

variable "replication_configuration" {
  type        = any
  default     = {}
  description = "Map containing cross-region replication configuration."
}

variable "request_payer" {
  type        = string
  default     = null
  description = "(Optional) Specifies who should bear the cost of Amazon S3 data transfer. Can be either BucketOwner or Requester. By default, the owner of the S3 bucket would incur the costs of any data transfer. See Requester Pays Buckets developer guide for more information."
}

variable "sse_algorithm" {
  type        = string
  default     = "aws:kms"
  description = "KMS Key for Replication"
}

variable "tags" {
  type        = map(string)
  default     = {}
  description = "(Optional) A mapping of tags to assign to the bucket."
}

variable "transition_default_minimum_object_size" {
  type        = string
  default     = null
  description = "The default minimum object size behavior applied to the lifecycle configuration. Valid values: all_storage_classes_128K (default), varies_by_storage_class"
}

variable "versioning" {
  type = map(string)
  default = {
    "status" : "enabled",
    "mfa_delete" : "disabled"
  }
  description = "Map containing versioning configuration."
}

variable "website" {
  type        = any
  default     = {}
  description = "Map containing static web-site hosting or redirect configuration."
}

variable "s3_log_acceleration_status" {
  type        = string
  default     = null
  description = "(Optional) Sets the accelerate configuration of an existing bucket. Can be Enabled or Suspended."
}

variable "s3_log_access_log_delivery_policy_source_accounts" {
  type        = list(string)
  default     = []
  description = "(Optional) List of AWS Account IDs should be allowed to deliver access logs to this bucket."
}

variable "s3_log_access_log_delivery_policy_source_buckets" {
  type        = list(string)
  default     = []
  description = "(Optional) List of S3 bucket ARNs which should be allowed to deliver access logs to this bucket."
}

variable "s3_log_attach_access_log_delivery_policy" {
  type        = bool
  default     = true
  description = "Controls if S3 bucket should have S3 access log delivery policy attached"
}

variable "s3_log_attach_elb_log_delivery_policy" {
  type        = bool
  default     = false
  description = "Controls if S3 bucket should have ELB log delivery policy attached"
}

variable "s3_log_attach_inventory_destination_policy" {
  type        = bool
  default     = false
  description = "Controls if S3 bucket should have bucket inventory destination policy attached."
}

variable "s3_log_attach_lb_log_delivery_policy" {
  type        = bool
  default     = false
  description = "Controls if S3 bucket should have ALB/NLB log delivery policy attached"
}

variable "s3_log_attach_policy" {
  type        = bool
  default     = false
  description = "Controls if S3 bucket should have bucket policy attached (set to `true` to use value of `policy` as bucket policy)"
}

variable "s3_log_attach_public_policy" {
  type        = bool
  default     = true
  description = "Controls if a user defined public bucket policy will be attached (set to `false` to allow upstream to apply defaults to the bucket)"
}

variable "s3_log_bucket" {
  type        = string
  default     = null
  description = "(Optional, Forces new resource) The name of the bucket. If omitted, Terraform will assign a random, unique name."
}

variable "s3_log_bucket_prefix" {
  type        = string
  default     = null
  description = "(Optional, Forces new resource) Creates a unique bucket name beginning with the specified prefix. Conflicts with bucket."
}

variable "s3_log_control_object_ownership" {
  type        = bool
  default     = false
  description = "Whether to manage S3 Bucket Ownership Controls on this bucket."
}

variable "s3_log_cors_rule" {
  type        = any
  default     = []
  description = "List of maps containing rules for Cross-Origin Resource Sharing."
}

variable "s3_log_expected_bucket_owner" {
  type        = string
  default     = null
  description = "The account ID of the expected bucket owner"
}

variable "s3_log_force_destroy" {
  type        = bool
  default     = false
  description = "(Optional, Default:false ) A boolean that indicates all objects should be deleted from the bucket so that the bucket can be destroyed without error. These objects are not recoverable."
}

variable "s3_log_grant" {
  type        = any
  default     = []
  description = "An ACL policy grant. Conflicts with `acl`"
}

variable "s3_log_intelligent_tiering" {
  type        = any
  default     = {}
  description = "Map containing intelligent tiering configuration."
}

variable "s3_log_inventory_configuration" {
  type        = any
  default     = {}
  description = "Map containing S3 inventory configuration."
}

variable "s3_log_inventory_self_source_destination" {
  type        = bool
  default     = false
  description = "Whether or not the inventory source bucket is also the destination bucket."
}

variable "s3_log_inventory_source_account_id" {
  type        = string
  default     = null
  description = "The inventory source account id."
}

variable "s3_log_inventory_source_bucket_arn" {
  type        = string
  default     = null
  description = "The inventory source bucket ARN."
}

variable "s3_log_kms_key_id" {
  type        = string
  default     = null
  description = "KMS Key for Replication"
}

variable "s3_log_lifecycle_rule" {
  type        = any
  default     = []
  description = "List of maps containing configuration of object lifecycle management."
}

variable "s3_log_logging" {
  type        = map(string)
  default     = {}
  description = "Map containing access bucket logging configuration."
}

variable "s3_log_metric_configuration" {
  type        = any
  default     = []
  description = "Map containing bucket metric configuration."
}

variable "s3_log_object_lock_configuration" {
  type        = any
  default     = {}
  description = "Map containing S3 object locking configuration."
}

variable "s3_log_object_lock_enabled" {
  type        = bool
  default     = false
  description = "Whether S3 bucket should have an Object Lock configuration enabled."
}

variable "s3_log_owner" {
  type        = map(string)
  default     = {}
  description = "Bucket owner's display name and ID. Conflicts with `acl`"
}

variable "s3_log_policy" {
  type        = string
  default     = null
  description = "(Optional) A valid bucket policy JSON document. Note that if the policy document is not specific enough (but still valid), Terraform may view the policy as constantly changing in a terraform plan. In this case, please make sure you use the verbose/specific version of the policy. For more information about building AWS IAM policy documents with Terraform, see the AWS IAM Policy Document Guide."
}

variable "s3_log_replication_configuration" {
  type        = any
  default     = {}
  description = "Map containing cross-region replication configuration."
}

variable "s3_log_request_payer" {
  type        = string
  default     = null
  description = "(Optional) Specifies who should bear the cost of Amazon S3 data transfer. Can be either BucketOwner or Requester. By default, the owner of the S3 bucket would incur the costs of any data transfer. See Requester Pays Buckets developer guide for more information."
}

variable "s3_log_sse_algorithm" {
  type        = string
  default     = "aws:kms"
  description = "KMS Key for Replication"
}

variable "s3_log_tags" {
  type        = map(string)
  default     = {}
  description = "(Optional) A mapping of tags to assign to the bucket."
}

variable "s3_log_transition_default_minimum_object_size" {
  type        = string
  default     = null
  description = "The default minimum object size behavior applied to the lifecycle configuration. Valid values: all_storage_classes_128K (default), varies_by_storage_class"
}

variable "s3_log_versioning" {
  type = map(string)
  default = {
    "status" : "enabled",
    "mfa_delete" : "disabled"
  }
  description = "Map containing versioning configuration."
}

variable "s3_log_website" {
  type        = any
  default     = {}
  description = "Map containing static web-site hosting or redirect configuration."
}
#>>GENERATE_CONTENT
