FROM base-image.dock.merck.com/dostack/golang/1.17.3/rel-2021w48.2:latest AS golang
FROM base-image.dock.merck.com/dostack/vscode-devcontainers/ubuntu/focal/latest:latest

ARG TF_VERSION=1.2.1 \
    TF_DOCS_VERSION=0.16.0 \
    TF_LINT_VERSION=0.42.2 \
    TF_SEC_VERSION=1.26.0 \
    TF_LINT_AWS_PLUGIN_VERSION=0.14.0 \
    TF_LINT_AZURERM_PLUGIN_VERSION=0.19.0 \
    CHECKOV_VERSION=2.0.930 \
    GITLEAKS_VERSION=8.13.0

COPY --from=golang /usr/local/go/ /usr/local/go/
ENV PATH="/usr/local/go/bin/:${PATH}" \
    HTTPS_PROXY="" \
    HTTP_PROXY=""

SHELL ["/bin/bash", "-o", "pipefail", "-c"]

RUN apt-get update && export DEBIAN_FRONTEND=noninteractive \
    && apt-get install -y --no-install-recommends gnupg software-properties-common curl wget vim nodejs npm \
# Install Terraform, pre-commit and checkov
    && curl -fsSL https://apt.releases.hashicorp.com/gpg | apt-key add - \
    && apt-add-repository "deb [arch=amd64] https://apt.releases.hashicorp.com $(lsb_release -cs) main" \
    && add-apt-repository ppa:deadsnakes/ppa \
    && apt-get update && apt-get install -y --no-install-recommends \
       terraform="${TF_VERSION}" \
       terraform-ls \
       python3.7 \
       python-is-python3 \
       python3-pip \
    && pip install --no-cache-dir \
       pre-commit \
       changelog-cli \
       checkov==${CHECKOV_VERSION} \
# Install tflint, terraform-docs, tfsec
    && wget -O terraform-docs.tgz https://github.com/terraform-docs/terraform-docs/releases/download/v"${TF_DOCS_VERSION}"/terraform-docs-v"${TF_DOCS_VERSION}"-linux-amd64.tar.gz \
    && tar -xzf terraform-docs.tgz terraform-docs \
    && rm terraform-docs.tgz \
    && chmod +x terraform-docs \
    && mv terraform-docs /usr/bin/ \
    && wget -O tflint.zip https://github.com/terraform-linters/tflint/releases/download/v"${TF_LINT_VERSION}"/tflint_linux_amd64.zip \
    && unzip tflint.zip \
    && rm tflint.zip \
    && mv tflint /usr/bin/ \
    && mkdir -p ~/.tflint.d/plugins/github.com/terraform-linters/tflint-ruleset-aws/${TF_LINT_AWS_PLUGIN_VERSION} \
    && wget -O tflint-ruleset-aws.zip  https://github.com/terraform-linters/tflint-ruleset-aws/releases/download/v"${TF_LINT_AWS_PLUGIN_VERSION}"/tflint-ruleset-aws_linux_amd64.zip \
    && unzip tflint-ruleset-aws.zip -d ~/.tflint.d/plugins/github.com/terraform-linters/tflint-ruleset-aws/${TF_LINT_AWS_PLUGIN_VERSION} \
    && rm tflint-ruleset-aws.zip \
    && mkdir -p ~/.tflint.d/plugins/github.com/terraform-linters/tflint-ruleset-azurerm/${TF_LINT_AZURERM_PLUGIN_VERSION} \
    && wget -O tflint-ruleset-azurerm.zip  https://github.com/terraform-linters/tflint-ruleset-azurerm/releases/download/v"${TF_LINT_AZURERM_PLUGIN_VERSION}"/tflint-ruleset-azurerm_linux_amd64.zip \
    && unzip tflint-ruleset-azurerm.zip -d ~/.tflint.d/plugins/github.com/terraform-linters/tflint-ruleset-azurerm/${TF_LINT_AZURERM_PLUGIN_VERSION} \
    && rm tflint-ruleset-azurerm.zip \
    && wget -O tfsec  https://github.com/aquasecurity/tfsec/releases/download/v"${TF_SEC_VERSION}"/tfsec-linux-amd64  \
    && chmod +x tfsec \
    && mv tfsec /usr/bin/ \
# Install AWS CLI v2
    && curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" \
    && unzip awscliv2.zip \
    && ./aws/install \
    && rm -fr ./aws \
# Install gitleaks
    && wget -q "https://github.com/zricethezav/gitleaks/releases/download/v${GITLEAKS_VERSION}/gitleaks_${GITLEAKS_VERSION}_linux_x64.tar.gz" \
    && tar -xvf gitleaks_${GITLEAKS_VERSION}_linux_x64.tar.gz \
    && mv gitleaks /bin/ \
    && chmod +x /bin/gitleaks \
# Install adr-log
    && npm install -g adr-log@2.2.0 npx \
# Download aws_get_credentials.sh
    && mkdir -p /home/<USER>/.local/bin \
    && cd /home/<USER>/.local/bin \
    && wget https://stash.merck.com/snippets/raw/bcab8fa6a1844f33abad8426671d7954/aws_get_credentials.sh \
    && chmod +x aws_get_credentials.sh \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*
