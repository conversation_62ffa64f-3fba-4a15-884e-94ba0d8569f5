data "aws_region" "current" {}

data "aws_ami" "msd_win" {
  count       = var.enable_ami_lookup ? 1 : 0
  most_recent = true
  owners      = ["404335411501"]
  name_regex  = "^Windows-2022-Base-Image.*"

  filter {
    name   = "root-device-type"
    values = ["ebs"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}

data "aws_partition" "current" {}

###############################
# Labels Module               #
###############################

module "labels" {
  source  = "artifacts.merck.com/terraform-iac-shared__internal/labels/aws"
  version = "~> 4.0"
  enabled = true

  appname     = var.resource_vars.appname
  region      = var.resource_vars.region
  attributes  = var.resource_vars.attributes
  label_order = var.resource_vars.label_order
  environment = var.resource_vars.environment
}

###############################
# EC2 Module                  #
###############################

module "ec2_instance" {
  # Update this source to use the Artifactory URL of a released version of the module: https://go.merck.com/iacreleases
  source  = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/ec2-instance/aws"
  version = "2.4.0"
  #enable_ebs_creation          = false
  name                          = "${module.labels.id}-testing"
  ami                           = var.ami != null ? var.ami : data.aws_ami.msd_win[0].id
  instance_type                 = var.instance_type
  availability_zone             = var.availability_zone
  subnet_id                     = var.subnet_id
  vpc_security_group_ids        = [module.ec2_security_group.security_group_id]
  key_name                      = var.key_pair_name
  iam_instance_profile          = var.iam_instance_profile
  user_data_replace_on_change   = var.user_data_replace_on_change
  enable_volume_tags            = var.enable_volume_tags
  root_block_device_volume_size = var.root_block_device_volume_size
  root_block_tags = {
    Name = "${module.labels.id}-root-blocks"
  }
  root_block_device_kms = module.aws_kms_key.key_arn
  ebs_volumes = [{
    device_name = "/dev/sdh"
    volume_size = 200
    volume_type = "gp3"
    kms_key_id  = module.aws_kms_key.key_arn
    throughput  = 200
    },
    {
      device_name = "/dev/sdf"
      volume_size = 200
      volume_type = "gp3"
      kms_key_id  = module.aws_kms_key.key_arn
      throughput  = 250
    }
  ]
  tags                     = var.tags
  default_tags             = var.default_tags
  iam_role_name            = "${module.labels.id}-win-role"
  iam_role_use_name_prefix = false
  iam_role_description     = "IAM role for EC2 instance"

  iam_role_tags = var.default_tags
  iam_role_policies = {
    AddTags                      = module.create_tags_policy.arn
    AmazonSSMManagedInstanceCore = "arn:${data.aws_partition.current.partition}:iam::aws:policy/AmazonSSMManagedInstanceCore"
    AmazonSSMPatchAssociation    = "arn:${data.aws_partition.current.partition}:iam::aws:policy/AmazonSSMPatchAssociation"
    AdministratorAccess          = "arn:${data.aws_partition.current.partition}:iam::aws:policy/AmazonS3ReadOnlyAccess"
  }
}

################################
# Supporting Resources         #
################################

module "create_tags_policy" {
  source        = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/iam/aws//modules/iam-policy"
  version       = "~> 2.2"
  create_policy = true
  name          = "${module.labels.id}-tags"
  policy = jsonencode(
    {
      "Version" : "2012-10-17"
      "Statement" : [
        {
          "Effect" : "Allow",
          "Action" : [
            "ec2:CreateTags"
          ],
          "Resource" : "*",
        }
      ]
    }
  )
  default_tags = var.default_tags
}

########## KMS Key ###########
module "aws_kms_key" {
  source       = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/kms/aws"
  version      = "~> 2.4"
  description  = "Key for encrypting EBS volumes and Cloudwatch Log Group"
  key_usage    = "ENCRYPT_DECRYPT"
  policy       = data.aws_iam_policy_document.kms_default.json
  tags         = {}
  default_tags = var.default_tags
  aliases      = ["alias/${module.labels.id}-ec2_cloudwatch"]
  multi_region = var.kms_use_multi_region
}

########## Cloudwatch Log Group ###########
module "cloudwatch_log_group" {
  depends_on        = [module.aws_kms_key]
  source            = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/cloudwatch/aws//modules/log-group"
  version           = "~> 2.0"
  name              = "${module.labels.id}-clgroup"
  retention_in_days = "7"
  kms_key_id        = module.aws_kms_key.key_arn
  tags              = var.tags
  default_tags      = var.default_tags
}

########## Security Group - EC2 ###########
module "ec2_security_group" {
  source                  = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/security-group/aws"
  version                 = "~> 2.5"
  name                    = "${module.labels.id}-winec2-sg"
  description             = "Security group for EC2 Instance example"
  vpc_id                  = var.vpc_id
  ingress_prefix_list_ids = var.prefix_list_ids
  ingress_with_cidr_blocks = [
    {
      from_port   = 3389
      to_port     = 3389
      protocol    = "tcp"
      description = "RDP"
      cidr_blocks = "************/32" # allow ssh access only for the aap https://share.merck.com/display/DSO/AAP+Low+Level+Architecture
    }
  ]
  egress_rules = ["all-all"]
  tags         = var.tags
  default_tags = var.default_tags
}
