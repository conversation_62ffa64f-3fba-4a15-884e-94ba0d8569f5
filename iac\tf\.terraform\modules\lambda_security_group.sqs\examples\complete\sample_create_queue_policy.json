{"ApproximateNumberOfMessages": "0", "ApproximateNumberOfMessagesDelayed": "0", "ApproximateNumberOfMessagesNotVisible": "0", "CreatedTimestamp": "", "DelaySeconds": "0", "KmsDataKeyReusePeriodSeconds": "", "KmsMasterKeyId": "", "LastModifiedTimestamp": "", "MaximumMessageSize": "262144", "MessageRetentionPeriod": "345600", "Policy": {"Version": "2012-10-17", "Id": "SQSPolicy", "Statement": [{"Sid": "AllowSendingMessages", "Effect": "Allow", "Principal": {"AWS": ["arn:aws:iam::************:role/Role1", "arn:aws:iam::************:role/Role2"]}, "Action": "sqs:SendMessage", "Resource": "arn:aws:sqs:${AWS::Region}:${AWS::AccountId}:${QueueName}", "Condition": {"ArnEquals": {"aws:SourceArn": "arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${SNSTopicName}"}, "StringEquals": {"aws:SourceAccount": "${AWS::AccountId}"}}}, {"Sid": "AllowReceivingMessages", "Effect": "Allow", "Principal": {"AWS": ["arn:aws:iam::************:role/Role3", "arn:aws:iam::************:role/Role4"]}, "Action": ["sqs:ReceiveMessage", "sqs:DeleteMessage"], "Resource": "arn:aws:sqs:${AWS::Region}:${AWS::AccountId}:${QueueName}"}]}, "QueueArn": "", "ReceiveMessageWaitTimeSeconds": "20", "SqsManagedSseEnabled": "true", "VisibilityTimeout": "30", "WizMetadata": {"region": "${AWS::Region}"}}