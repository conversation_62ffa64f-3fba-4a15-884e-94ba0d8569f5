{"Version": "2012-10-17", "Statement": [{"Effect": "Allow", "Action": ["dynamodb:DeleteItem", "dynamodb:GetItem", "dynamodb:PutItem", "iam:CreateServiceLinkedRole", "kms:<PERSON><PERSON><PERSON><PERSON><PERSON>", "kms:C<PERSON><PERSON><PERSON>", "kms:DeleteAlias", "kms:DescribeKey", "kms:EnableKeyRotation", "kms:GetKeyPolicy", "kms:GetKeyRotationStatus", "kms:ListAliases", "kms:ListResourceTags", "kms:PutKeyPolicy", "kms:ScheduleKeyDeletion", "kms:TagResource", "kms:UntagResource", "s3:GetObject", "s3:ListBucket", "s3:PutObject", "sts:<PERSON><PERSON>Role", "SNS:<PERSON><PERSON><PERSON><PERSON><PERSON>", "SNS:TagResource", "SNS:SetTopicAttributes", "SNS:GetTopicAttributes", "SNS:ListTagsForResource", "SNS:DeleteTopic", "SNS:Subscribe", "SNS:GetSubscriptionAttributes", "SNS:Unsubscribe", "SNS:UntagResource"], "Resource": "*"}]}