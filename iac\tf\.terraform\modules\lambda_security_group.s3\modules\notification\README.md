#  S3 Notification Wrapper Module

> [!TIP]
> Read the [root level README](../../README.md) first!

This is the Merck wrapper for [S3 notification community sub-module](https://github.com/merck-gen/iac-shared-tf-module-aws-s3-bucket-ext/tree/main/modules/notification). The purpose of this module is to help you create a [S3 notification](https://cloud.merck.com/documentation/native-services/aws/s3/index.html) for a particular event in your AWS account.

<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | >= 5.0 |
| <a name="requirement_local"></a> [local](#requirement\_local) | >= 2.4.0 |

## Providers

No providers.

## Modules

| Name | Source | Version |
|------|--------|---------|
| <a name="module_notification"></a> [notification](#module\_notification) | artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/s3-bucket/aws//modules/notification | 4.2.2 |

## Resources

No resources.

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_bucket"></a> [bucket](#input\_bucket) | Name of S3 bucket to use | `string` | `""` | no |
| <a name="input_bucket_arn"></a> [bucket\_arn](#input\_bucket\_arn) | ARN of S3 bucket to use in policies | `string` | `null` | no |
| <a name="input_create"></a> [create](#input\_create) | Whether to create this resource or not? | `bool` | `true` | no |
| <a name="input_create_sns_policy"></a> [create\_sns\_policy](#input\_create\_sns\_policy) | Whether to create a policy for SNS permissions or not? | `bool` | `true` | no |
| <a name="input_create_sqs_policy"></a> [create\_sqs\_policy](#input\_create\_sqs\_policy) | Whether to create a policy for SQS permissions or not? | `bool` | `true` | no |
| <a name="input_eventbridge"></a> [eventbridge](#input\_eventbridge) | Whether to enable Amazon EventBridge notifications | `bool` | `null` | no |
| <a name="input_lambda_notifications"></a> [lambda\_notifications](#input\_lambda\_notifications) | Map of S3 bucket notifications to SQS queue.<pre>type = map(object({<br/>  id             = optional(string)<br/>  function_name  = string<br/>  function_arn   = string<br/>  events         = list(string)<br/>  filter_prefix  = optional(string)<br/>  filter_suffix  = optional(string)<br/>  qualifier      = optional(string)<br/>  source_account = optional(string)<br/>}))</pre> | <pre>map(object({<br/>    id             = optional(string)<br/>    function_name  = string<br/>    function_arn   = string<br/>    events         = list(string)<br/>    filter_prefix  = optional(string)<br/>    filter_suffix  = optional(string)<br/>    qualifier      = optional(string)<br/>    source_account = optional(string)<br/>  }))</pre> | `{}` | no |
| <a name="input_sns_notifications"></a> [sns\_notifications](#input\_sns\_notifications) | Map of S3 bucket notifications to SNS topic.<pre>type = mmap(object({<br/>  id            = optional(string)<br/>  topic_arn     = string<br/>  events        = list(string)<br/>  filter_prefix = optional(string)<br/>  filter_suffix = optional(string)<br/>}))</pre> | <pre>map(object({<br/>    id            = optional(string)<br/>    topic_arn     = string<br/>    events        = list(string)<br/>    filter_prefix = optional(string)<br/>    filter_suffix = optional(string)<br/>  }))</pre> | `{}` | no |
| <a name="input_sqs_notifications"></a> [sqs\_notifications](#input\_sqs\_notifications) | Map of S3 bucket notifications to SQS queue.<pre>type = mmap(object({<br/>  id            = optional(string)<br/>  queue_arn     = string<br/>  queue_id      = optional(string)<br/>  events        = list(string)<br/>  filter_prefix = optional(string)<br/>  filter_suffix = optional(string)<br/>}))</pre> | <pre>map(object({<br/>    id            = optional(string)<br/>    queue_arn     = string<br/>    queue_id      = optional(string)<br/>    events        = list(string)<br/>    filter_prefix = optional(string)<br/>    filter_suffix = optional(string)<br/>  }))</pre> | `{}` | no |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_s3_bucket_notification_id"></a> [s3\_bucket\_notification\_id](#output\_s3\_bucket\_notification\_id) | ID of S3 bucket |
<!-- END_TF_DOCS -->
