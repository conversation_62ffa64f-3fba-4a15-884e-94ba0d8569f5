variable "availability_zone" {
  description = "AZ to start the instance in"
  type        = string
  default     = null
}

variable "instance_type" {
  description = "The type of instance to start"
  type        = string
  default     = "t3a.micro"
}

variable "key_pair_name" {
  description = "Key name of the Key Pair to use for the instance; which can be managed using the `aws_key_pair` resource"
  type        = string
  default     = null
}

variable "tags" {
  description = "map of tags to pass to labels module"
  type        = map(string)
  default     = {}
}

variable "iam_instance_profile" {
  description = "IAM Instance Profile to launch the instance with. Specified as the name of the Instance Profile"
  type        = string
}

variable "root_block_device_volume_size" {
  description = "Customize details about the root block device of the instance"
  type        = string
  default     = 50
}

variable "subnet_id" {
  description = "The VPC Subnet ID to launch in"
  type        = string
  default     = null
}

variable "user_data_replace_on_change" {
  description = "When used in combination with user_data or user_data_base64 will trigger a destroy and recreate when set to true. Defaults to false if not set."
  type        = bool
  default     = false
}

variable "enable_volume_tags" {
  description = "Whether to enable volume tags (if enabled it conflicts with root_block_device tags)"
  type        = bool
  default     = true
}

################################################################################
# IAM Role / Instance Profile
################################################################################

variable "default_tags" {
  description = "A map of default tags to be applied on each resource. You can get required tags [here](https://cloud.merck.com/documentation/compliance/tagging-standards/index.html)"
  type        = map(string)
}

variable "resource_vars" {
  description = "map of tags to pass to labels module"
  type = object({
    appname     = string
    region      = string
    attributes  = list(string)
    label_order = list(string)
    environment = optional(string, "dev")
  })
}

variable "region" {
  description = "AWS Region"
  type        = string
  default     = "us-east-1"
}

variable "partition" {
  type        = string
  default     = "aws"
  description = "Second part of aws arn. Used only in the provider configuration."
}

variable "deployment_role" {
  description = "Terraform Deployment Role"
  type        = string
}

variable "account_no" {
  description = "AWS account number to deploy to"
  type        = string
}

variable "vpc_id" {
  description = "VPC ID to deploy to"
  type        = string
}

variable "prefix_list_ids" {
  description = "Prefix list"
  type        = list(string)
  default     = null
}

variable "kms_use_multi_region" {
  description = "A switch to turn multi_region on/off for kms"
  type        = bool
  default     = true
}
variable "ami" {
  description = "ID of AMI to use for the instance"
  type        = string
  default     = null
}

variable "enable_ami_lookup" {
  type    = bool
  default = true
}
