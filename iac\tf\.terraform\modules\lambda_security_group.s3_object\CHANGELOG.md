# Changelog

​
All notable changes to this repository will be documented in this file.
​
The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

Please use the boilerplate code below to fill in information related to your change (create a new section for each new version).

## [Unreleased]

## [[3.3.0] - 2025-02-11](https://github.com/merck-gen/iac-shared-tf-module-aws-s3-bucket-wrapper/releases/tag/3.3.0)


### Changed

- `logging` - The `logging` variable is now made as optional. 
   The conditions for the logging configurations are as explained below:
    - If `logging` must be disabled, then create should be set to false, and target_bucket should be null.
    - If we want to use a log bucket that already exists, then create should be set to false, and target_bucket should be set to "existing_bucket_name".
    - If `logging` must be enabled, then create should be set to true

## [[3.2.0] - 2024-12-09](https://github.com/merck-gen/iac-shared-tf-module-aws-s3-bucket-wrapper/releases/tag/3.2.0)

### Added

- module `s3_log_bucket`:
  - inputs:
    - `s3_log_transition_default_minimum_object_size`: (Optional) The default minimum object size behavior applied to the lifecycle configuration. Valid values: all_storage_classes_128K (default), varies_by_storage_class


- module `s3_bucket_wrapper`:
  - inputs:
    - `transition_default_minimum_object_size`: (Optional) The default minimum object size behavior applied to the lifecycle configuration. Valid values: all_storage_classes_128K (default), varies_by_storage_class

### Changed

- module `s3_log_bucket`:
  - source changed from v4.1.2 to v4.2.2, you can find the changes made to the ext module [here](https://github.com/merck-gen/iac-shared-tf-module-aws-s3-bucket-ext/compare/v4.1.2...v4.2.2)

- module `s3_bucket_wrapper`:
  - source changed from v4.1.2 to v4.2.2, you can find the changes made to the ext module [here](https://github.com/merck-gen/iac-shared-tf-module-aws-s3-bucket-ext/compare/v4.1.2...v4.2.2)



## [[3.1.0] - 2024-11-15](https://github.com/merck-gen/iac-shared-tf-module-aws-s3-bucket-wrapper/releases/tag/3.1.0)


### Added

- added variable for `block_public_access` in root folder variables.tf

### Changed

- `sse_algorithm`: can now be configured

## [[3.0.2] - 2024-10-15](https://github.com/merck-gen/iac-shared-tf-module-aws-s3-bucket-wrapper/releases/tag/3.0.2)


> [!IMPORTANT]  
> If you're using submodules from this module, please use this release (`3.0.2` instead of `3.0.0` or `3.0.1`) because of missing moved blocks for the submodules.

### Fixed
- added moved blocks for each submodule


## [[3.0.1] - 2024-10-03](https://github.com/merck-gen/iac-shared-tf-module-aws-s3-bucket-wrapper/releases/tag/3.0.1)


- module has been only rereleased, no changes in code has been made

## [3.0.0] - 2024-08-05

### Changed 
- wrapper-modules folder path to [/modules](modules/)
- following are the new path for sub modules
  - `wrapper-modules/notification` ->  [modules/notification](./modules/notification)
  - `wrapper-modules/object` -> [modules/object](./modules/object)

## [2.0.0] - 2024-07-22

### Added
- `.config.yaml` file for module generator. 
- ouputs 
  - `s3_bucket_bucket_domain_name`
  - `s3_bucket_bucket_regional_domain_name` 
  - `s3_bucket_hosted_zone_id`
  - `s3_bucket_region`
  - `s3_bucket_lifecycle_configuration_rules_region`
  - `output_s3_bucket_policy`
  - `s3_log_bucket_bucket_domain_name`
  - `s3_log_bucket_bucket_regional_domain_name`
  - `s3_log_bucket_hosted_zone_id`
  - `s3_log_bucket_lifecycle_configuration_rules`
  - `s3_log_bucket_policy`
  - `s3_log_bucket_website_domain`
  - `s3_log_bucket_website_endpoint`

- Validation condition for `default_tags` variable to validate the required tags.

### Removed
- variable `s3_log_create_bucket`. The functionality of this variable is handled by `var.logging.create`

## [1.8.3] - 2024-05-17

### Fixed
- S3 object and notification module outputs.

## [1.8.2] - 2024-05-09

### Changed
- source for `s3-bucket-ext` from `4.1.1` to `4.1.2` in the below files. [Ext PR](https://github.com/merck-gen/iac-shared-tf-module-aws-s3-bucket-ext/pull/36)
    - main.tf 
    - wrapper-modules/notification/main.tf
    - wrapper-modules/object/main.tf

### Fixed
- Typo in description of the variable `access_log_delivery_policy_source_buckets`.

## [1.8.1] - 2024-04-02

### Changed
- source for `s3-bucket-ext` from `4.1.0` to `4.1.1` in the below files. [Ext PR](https://github.com/merck-gen/iac-shared-tf-module-aws-s3-bucket-ext/pull/35)
    - main.tf 
    - wrapper-modules/notification/main.tf
    - wrapper-modules/object/main.tf
      - Note: this increase in `s3-bucket-ext` does not qualify for a minor version increase in this wrapper since new functionality was not introduced that affects this repo
- README.md has included relevant service site link.

## [1.8.0] - 2024-03-11

### Changed

- source for `s3-bucket-ext` from `3.15.1` to `4.1.0` in main.tf - [Ext PR](https://github.com/merck-gen/iac-shared-tf-module-aws-s3-bucket-ext/pull/34/files) - they had to bump the major version because of the aws provider requirement change to >= 5.27, but we already required >= 5.0 therefore this is only minor version change for us
- updated dependency modules version to latest in all the examples.
  - examples
    - s3
      - `aws_kms_key` from 1.0.3 to 2.2.1
    - s3-notification
      - `sns_topic` from 1.2.0 to 2.0.0
      - `aws_kms_key_sns_topic` from 2.0.1 to 2.2.1
      - `aws_kms_key_s3_bucket` from 2.0.1 to 2.2.1
    - s3-notification-lambda
      - `aws_kms_key_s3_bucket` from 2.0.1 to 2.2.1
      - `lambda` from 3.0.0 to 3.3.0
      - `lambda_security_group` from 2.0.0 to 2.2.1      
      - `cloudwatch_log_group` from 1.1.1 to 1.4.0
      - `aws_kms_key_lambda` from 2.0.1 to 2.2.1
    - s3-object
      - `aws_kms_key_primary` from 2.0.1 to 2.2.1
    - s3-with-logging
      - `aws_kms_key` from 1.0.3 to 2.2.1
    - s3-with-replica
      - `aws_kms_key` from 1.0.3 to 2.2.1
      - `aws_kms_key_replica` from 1.0.3 to 2.2.1
- attribute `label_order` of the variable `resource_vars` in the terraform.dev.tfvars for all the examples.
- all README files were updated

### Added

- input variables:
  - attributes to `logging variable`:
    - `target_object_key_format` object as an attribute, check ([changes](https://github.com/merck-gen/iac-shared-tf-module-aws-s3-bucket-wrapper/pull/60/files#diff-05b5a57c136b6ff596500bcbfdcff145ef6cddea2a0e86d184d9daa9a65a288eR114-R122))
- wrapper submodules:
  -  `notification`:
      - `lambda_notifications` ([variable](https://github.com/merck-gen/iac-shared-tf-module-aws-s3-bucket-wrapper/pull/60/files#diff-80bc8fb46d4b30ca3df79017f37f0e636eada5ffcce27eb05715bad7e504f2a3)):
         - added `qualifier` and `source_account` attributes
- variable `override_default_tags` in `object` wrapper-modules ([Ext Commit](https://github.com/merck-gen/iac-shared-tf-module-aws-s3-bucket-ext/pull/34/commits/f9e1740cafe597f2764a0d2ee7dbd34a0e19753c))


## [1.7.0] - 2024-01-15

### Changed
- README.md `Modules` section.
- adoption tags module version to `1.0.0` 
- labels module version to `3.0.0`

## [1.6.0] - 2023-11-14

### Added
- `notification` and `object` [sub-modules](wrapper-modules/)
- s3-notification [example](examples/s3-notification/)
- s3-notification-lambda [example](examples/s3-notification-lambda/)
- s3-object [example](examples/s3-object/)
  
## [1.5.1] - 2023-11-01

### Changed
- default value for variable `s3_log_attach_access_log_delivery_policy`
- log bucket outputs
- default value for argument `access_log_delivery_policy_source_buckets` of log bucket

## [1.5.0] - 2023-09-21

### Added
- variable `attach_analytics_destination_policy`, `analytics_source_account_id` , `analytics_source_bucket_arn` ,  `analytics_self_source_destination ` , `acl` , `analytics_configuration` 

### Changed
- ext source from `v3.14.1` -> `v3.15.1`

### Updated
- README.md examples 

## [1.4.5] - 2023-09-21

### Added
- variable `bucket_key_enabled`

### Changed 
- README.md

## [1.4.4] - 2023-09-13

### Added
- consumption.tf
- `tags` module

### Changed 
- README.md
- main.tf
- versions.tf

## [1.4.3] - 2023-08-24

### Updated

- Module `source` version for ext set to `3.14.1`

## [1.4.2] - 2023-08-24

### Added
- new attribute called `create` within the variable `logging`
- example `s3_with-logging`

### Changed
- README

### Fixed
- Fixed the logging issue
- Fixed Jenkins annotation in catalog-info.yaml

## [1.4.1] - 2023-07-31

### Added
Added content to catalog-info.yaml file
  - Fixed metadata
  - Added links


## [1.4.0] - 2023-07-26

### Added
Added 2 files and a sym link:
  - Files: Catalog-info.yaml, mkdocs.yaml
  - Sym link: Pointing README.md to /docs/index.md

## [1.3.0] - 2023-07-19

### Added

- Variable `attach_deny_incorrect_encryption_headers` in s3-bucket-wrapper module
- Variable `attach_deny_incorrect_kms_key_sse` in s3-bucket-wrapper module
- Variable `allowed_kms_key_arn` in s3-bucket-wrapper module
- Variable `attach_deny_unencrypted_object_uploads` in s3-bucket-wrapper module

### Updated

- Module `source` version for ext set to `3.14.0`

## [1.2.1] - 2023-07-18

### Fixed

- s3-with-replica example replication permissions
