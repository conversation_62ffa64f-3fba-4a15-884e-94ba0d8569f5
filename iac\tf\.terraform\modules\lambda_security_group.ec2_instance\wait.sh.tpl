#!/bin/bash
set -e
export PRE_AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID
export PRE_AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY
export PRE_AWS_SESSION_TOKEN=$AWS_SESSION_TOKEN
export AWS_REGION=${REGION}

assume_role(){
    export $(printf "AWS_ACCESS_KEY_ID=%s AWS_SECRET_ACCESS_KEY=%s AWS_SESSION_TOKEN=%s" \
        $(AWS_ACCESS_KEY_ID=$PRE_AWS_ACCESS_KEY_ID AWS_SECRET_ACCESS_KEY=$PRE_AWS_SECRET_ACCESS_KEY AWS_SESSION_TOKEN=$PRE_AWS_SESSION_TOKEN aws sts assume-role \
        --role-arn ${DEPLOYMENT_ROLE_ARN} \
        --role-session-name waiting_for_instance \
        --query "Credentials.[<PERSON><PERSON><PERSON><PERSON>d,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON>]" \
        --output text))
}

assume_role

already_tried=0
until [ 1 -eq $(aws ec2 describe-tags --filters "Name=key,Values=${TAG_NAME}" "Name=resource-id,Values=${INSTANCE_ID}" --query="length(Tags[*].ResourceId)") ] ; do
    sleep ${SLEEP_TIME}
    (( already_tried+=1 ))
    if [ $already_tried -gt ${NUM_OF_CYCLES} ]; then
        echo "Waiting for too long, exit 1!"
        exit 1
    fi
    assume_role
done

if [ $(aws ec2 describe-tags --filters "Name=key,Values=${TAG_NAME}" "Name=resource-id,Values=${INSTANCE_ID}" --query='length(Tags[?Value==`${FAILED_VALUE}`])') -gt 0 ]; then
    echo "Instance failed to initialize!"
    echo $(aws ec2 describe-tags --filters "Name=key,Values=${TAG_NAME}" "Name=resource-id,Values=${INSTANCE_ID}" --query='Tags[?Value==`${FAILED_VALUE}`].ResourceId' )
    exit 1
else 
    exit 0
fi