#>>GENERATE_CONTENT

variable "bucket" {
  type        = string
  default     = ""
  description = "Name of S3 bucket to use"
}

variable "bucket_arn" {
  type        = string
  default     = null
  description = "ARN of S3 bucket to use in policies"
}

variable "create" {
  type        = bool
  default     = true
  description = "Whether to create this resource or not?"
}

variable "create_sns_policy" {
  type        = bool
  default     = true
  description = "Whether to create a policy for SNS permissions or not?"
}

variable "create_sqs_policy" {
  type        = bool
  default     = true
  description = "Whether to create a policy for SQS permissions or not?"
}

variable "eventbridge" {
  type        = bool
  default     = null
  description = "Whether to enable Amazon EventBridge notifications"
}

variable "lambda_notifications" {
  type = map(object({
    id             = optional(string)
    function_name  = string
    function_arn   = string
    events         = list(string)
    filter_prefix  = optional(string)
    filter_suffix  = optional(string)
    qualifier      = optional(string)
    source_account = optional(string)
  }))
  default     = {}
  description = <<-DESCRIPTION
    Map of S3 bucket notifications to SQS queue.
    ```
    type = map(object({
      id             = optional(string)
      function_name  = string
      function_arn   = string
      events         = list(string)
      filter_prefix  = optional(string)
      filter_suffix  = optional(string)
      qualifier      = optional(string)
      source_account = optional(string)
    }))
    ```
  DESCRIPTION
}

variable "sns_notifications" {
  type = map(object({
    id            = optional(string)
    topic_arn     = string
    events        = list(string)
    filter_prefix = optional(string)
    filter_suffix = optional(string)
  }))
  default     = {}
  description = <<-DESCRIPTION
    Map of S3 bucket notifications to SNS topic.
    ```
    type = mmap(object({
      id            = optional(string)
      topic_arn     = string
      events        = list(string)
      filter_prefix = optional(string)
      filter_suffix = optional(string)
    }))
    ```
  DESCRIPTION
}

variable "sqs_notifications" {
  type = map(object({
    id            = optional(string)
    queue_arn     = string
    queue_id      = optional(string)
    events        = list(string)
    filter_prefix = optional(string)
    filter_suffix = optional(string)
  }))
  default     = {}
  description = <<-DESCRIPTION
    Map of S3 bucket notifications to SQS queue.
    ```
    type = mmap(object({
      id            = optional(string)
      queue_arn     = string
      queue_id      = optional(string)
      events        = list(string)
      filter_prefix = optional(string)
      filter_suffix = optional(string)
    }))
    ```
  DESCRIPTION
}
#>>GENERATE_CONTENT