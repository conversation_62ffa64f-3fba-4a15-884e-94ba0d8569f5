#>>GENERATE_CONTENT

data "local_file" "version" {
  filename = "${abspath(path.module)}/VERSION"
}

module "tags" {
  source = "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz"

  default_tags   = var.default_tags
  input_tags     = var.tags
  module_name    = "iac-shared-tf-module-aws-s3-bucket-wrapper"
  module_version = data.local_file.version.content
}

#>>GENERATE_CONTENT