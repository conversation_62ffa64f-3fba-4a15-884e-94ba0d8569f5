variable "input_tags" {
  description = "tags to which pattern/wrapper level information will be merged"
  type        = map(string)
}

variable "module_name" {
  description = "name of the wrapper/pattern"
  type        = string
}

variable "module_version" {
  description = "version of the wrapper/pattern"
  type        = string
}

variable "default_tags" {
  type        = map(string)
  description = "map of mandatory default tags"
}
