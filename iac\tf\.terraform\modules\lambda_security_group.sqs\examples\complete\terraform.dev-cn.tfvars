######################################################################################
# Required "User Provided" Variables
# you must modify these with application specific information
######################################################################################

# Labels - this will create consistent naming and tagging across all resources
# All labels become part of the AWS resource name based on label_order
resource_vars = {
  appname     = "tft"
  region      = "cn-north-1"
  attributes  = ["wrap", "sqs"]
  environment = "dev"
  label_order = ["appname", "environment"]
}

# Tags - the following tags will be added across all resources
default_tags = {
  DataClassification = "Proprietary"
  Consumer           = "<EMAIL>"
  Application        = "tft"
  Environment        = "Development"
  Service            = "TFT-Development"
}

# AWS Account Information
account_no      = "************"
deployment_role = "deployment-role"
region          = "cn-north-1"
partition       = "aws-cn"

######################################################################################
# Optional "User Provided" Variables
# you can modify the values below to suit your application as needed
######################################################################################

# Queue

fifo_queue                 = false
use_name_prefix            = false
receive_wait_time_seconds  = null
visibility_timeout_seconds = null
create_dlq                 = true
queue_policy_statements = {
  account = {
    sid = "AccountReadWrite"
    actions = [
      "sqs:SendMessage",
      "sqs:ReceiveMessage",
    ]
    principals = [
      {
        type        = "AWS"
        identifiers = ["arn:aws-cn:iam::************:root"]
      }
    ]
  }
}
create_dlq_queue_policy = true
dlq_queue_policy_statements = {
  account = {
    sid = "AccountReadWrite"
    actions = [
      "sqs:SendMessage",
      "sqs:ReceiveMessage",
    ]
    principals = [
      {
        type        = "AWS"
        identifiers = ["arn:aws-cn:iam::************:root"]
      }
    ]
  }
}
