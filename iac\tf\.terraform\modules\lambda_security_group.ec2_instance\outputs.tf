#>>GENERATE_CONTENT
output "ami" {
  value       = module.ec2_instance_wrapper.ami
  description = "AMI ID that was used to create the instance"
}

output "arn" {
  value       = module.ec2_instance_wrapper.arn
  description = "The ARN of the instance"
}

output "availability_zone" {
  value       = module.ec2_instance_wrapper.availability_zone
  description = "The availability zone of the created instance"
}

output "capacity_reservation_specification" {
  value       = module.ec2_instance_wrapper.capacity_reservation_specification
  description = "Capacity reservation specification of the instance"
}

output "ebs_block_device" {
  value       = module.ec2_instance_wrapper.ebs_block_device
  description = "EBS block device information"
}

output "ephemeral_block_device" {
  value       = module.ec2_instance_wrapper.ephemeral_block_device
  description = "Ephemeral block device information"
}

output "iam_instance_profile_arn" {
  value       = module.ec2_instance_wrapper.iam_instance_profile_arn
  description = "ARN assigned by AWS to the instance profile"
}

output "iam_instance_profile_id" {
  value       = module.ec2_instance_wrapper.iam_instance_profile_id
  description = "Instance profile's ID"
}

output "iam_instance_profile_unique" {
  value       = module.ec2_instance_wrapper.iam_instance_profile_unique
  description = "Stable and unique string identifying the IAM instance profile"
}

output "iam_role_arn" {
  value       = module.ec2_instance_wrapper.iam_role_arn
  description = "The Amazon Resource Name (ARN) specifying the IAM role"
}

output "iam_role_name" {
  value       = module.ec2_instance_wrapper.iam_role_name
  description = "The name of the IAM role"
}

output "iam_role_unique_id" {
  value       = module.ec2_instance_wrapper.iam_role_unique_id
  description = "Stable and unique string identifying the IAM role"
}

output "id" {
  value       = module.ec2_instance_wrapper.id
  description = "The ID of the instance"
}

output "instance_state" {
  value       = module.ec2_instance_wrapper.instance_state
  description = "The state of the instance"
}

output "ipv6_addresses" {
  value       = module.ec2_instance_wrapper.ipv6_addresses
  description = "The IPv6 address assigned to the instance, if applicable"
}

output "outpost_arn" {
  value       = module.ec2_instance_wrapper.outpost_arn
  description = "The ARN of the Outpost the instance is assigned to"
}

output "password_data" {
  value       = module.ec2_instance_wrapper.password_data
  description = "Base-64 encoded encrypted password data for the instance. Useful for getting the administrator password for instances running Microsoft Windows. This attribute is only exported if `get_password_data` is true"
}

output "primary_network_interface_id" {
  value       = module.ec2_instance_wrapper.primary_network_interface_id
  description = "The ID of the instance's primary network interface"
}

output "private_dns" {
  value       = module.ec2_instance_wrapper.private_dns
  description = "The private DNS name assigned to the instance. Can only be used inside the Amazon EC2, and only available if you've enabled DNS hostnames for your VPC"
}

output "private_ip" {
  value       = module.ec2_instance_wrapper.private_ip
  description = "The private IP address assigned to the instance"
}

output "public_dns" {
  value       = module.ec2_instance_wrapper.public_dns
  description = "The public DNS name assigned to the instance. For EC2-VPC, this is only available if you've enabled DNS hostnames for your VPC"
}

output "public_ip" {
  value       = module.ec2_instance_wrapper.public_ip
  description = "The public IP address assigned to the instance, if applicable."
}

output "root_block_device" {
  value       = module.ec2_instance_wrapper.root_block_device
  description = "Root block device information"
}

output "spot_bid_status" {
  value       = module.ec2_instance_wrapper.spot_bid_status
  description = "The current bid status of the Spot Instance Request"
}

output "spot_instance_id" {
  value       = module.ec2_instance_wrapper.spot_instance_id
  description = "The Instance ID (if any) that is currently fulfilling the Spot Instance request"
}

output "spot_request_state" {
  value       = module.ec2_instance_wrapper.spot_request_state
  description = "The current request state of the Spot Instance Request"
}

output "tags_all" {
  value       = module.ec2_instance_wrapper.tags_all
  description = "A map of tags assigned to the resource, including those inherited from the provider default_tags configuration block"
}

#>>GENERATE_CONTENT