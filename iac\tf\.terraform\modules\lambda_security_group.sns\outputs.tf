#>>GENERATE_CONTENT
output "subscriptions" {
  value       = module.ext.subscriptions
  description = "Map of subscriptions created and their attributes"
}

output "topic_arn" {
  value       = module.ext.topic_arn
  description = "The ARN of the SNS topic, as a more obvious property (clone of id)"
}

output "topic_beginning_archive_time" {
  value       = module.ext.topic_beginning_archive_time
  description = "The oldest timestamp at which a FIFO topic subscriber can start a replay"
}

output "topic_id" {
  value       = module.ext.topic_id
  description = "The ARN of the SNS topic"
}

output "topic_name" {
  value       = module.ext.topic_name
  description = "The name of the topic"
}

output "topic_owner" {
  value       = module.ext.topic_owner
  description = "The AWS Account ID of the SNS topic owner"
}

#>>GENERATE_CONTENT