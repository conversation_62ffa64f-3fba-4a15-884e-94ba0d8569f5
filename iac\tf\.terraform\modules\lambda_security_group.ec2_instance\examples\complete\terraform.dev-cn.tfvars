######################################################################################
# Required "User Provided" Variables
# you must modify these with application specific information
######################################################################################

# Labels - this will create consistent naming and tagging across all resources
# All labels become part of the AWS resource name based on label_order
resource_vars = {
  appname     = "tft"
  region      = "cn-north-1"
  attributes  = ["wrap", "ec2"]
  label_order = ["appname", "region"]
  environment = "dev"
}

# Tags - the following tags will be added across all resources
default_tags = {
  Application        = "tft"
  Consumer           = "<EMAIL>"
  Environment        = "Development"
  DataClassification = "Proprietary"
  Service            = "TFT-Development"
}

# AWS Account Information

subnet_id       = "subnet-0dc57cf167a4181e5"
region          = "cn-north-1"
account_no      = "************"
deployment_role = "deployment-role"
vpc_id          = "vpc-0f666efa794251c3e"
partition       = "aws-cn"

######################################################################################
# Optional "User Provided" Variables
# you can modify the values below to suit your application as needed
######################################################################################

ami                  = "ami-028f21d59942bafcb"
instance_type        = "t3a.small"
availability_zone    = "cn-north-1a"
key_pair_name        = "iac-shared"
iam_instance_profile = "myapp"

user_data_replace_on_change = true

enable_volume_tags = false #conflicts with root_block_device

root_block_device_volume_size = 120

kms_use_multi_region = false

enable_ami_lookup = false

