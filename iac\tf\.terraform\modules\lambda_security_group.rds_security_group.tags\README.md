# Adoption Tags Internal module

[![Build Status](https://builds.merck.com/buildStatus/icon?job=GITHUB%2Fiac-shared%2Fiac-shared-tf-module-aws-adoption-tags-int%2Fmultibranch-job%2Fmain)](https://builds.merck.com/job/GITHUB/job/iac-shared/job/iac-shared-tf-module-aws-adoption-tags-int/job/multibranch-job/job/main/)

## Introduction
This is the Merck Adoption Tags internal module. The purpose of this module is to attach wrapper level and pattern level information in the tags. This will help in metric and observability tracker. 

## Pre-requisites
Before deploying this module you will need to have the following:

| | |
|------|---------|
|Terraform tool|Used to do the deployment based on the IaC code|

| :warning:  Note!        |
|:---------------------------|
| Please check what is the latest available release version of this module: [IAC Shared library releases](https://go.merck.com/iacreleases)  

## Usage

This module is used at the root level for each and every pattern and module. This will ensure that all resources have pattern and wrapper level information in the final tags. Please refer [ Tagging approach for Terraform Infrastructure Patterns and Modules page](https://share.merck.com/display/IACS/Tagging+approach+for+Terraform+Infrastructure+Patterns+and+Modules) 

## Contributing
Everyone is welcome to contribute to the code of this module! Please see the [instructions on how to contribute on IAC Shared home page](https://go.merck.com/iac).

## Contact
In case any issues regarding the module please comment on the [MS Teams Infrastructure as Code channel](https://teams.microsoft.com/l/channel/19%3acc5325594d1543069ea7270bcd393da1%40thread.skype/Infrastructure%2520as%2520Code?groupId=7d021e32-5295-41c2-ad7c-24f0f6de8ff4&tenantId=a00de4ec-48a8-43a6-be74-e31274e2060d) or connect via email <<EMAIL>>.


<!-- BEGIN_TF_DOCS -->
## Requirements

| Name | Version |
|------|---------|
| <a name="requirement_terraform"></a> [terraform](#requirement\_terraform) | >= 1.2.0 |
| <a name="requirement_aws"></a> [aws](#requirement\_aws) | >= 5.0 |

## Providers

No providers.

## Modules

No modules.

## Resources

No resources.

## Inputs

| Name | Description | Type | Default | Required |
|------|-------------|------|---------|:--------:|
| <a name="input_default_tags"></a> [default\_tags](#input\_default\_tags) | map of mandatory default tags | `map(string)` | n/a | yes |
| <a name="input_extra_tags"></a> [extra\_tags](#input\_extra\_tags) | Additional tags (e.g. map(`BusinessUnit`,`XYZ`). | `map(string)` | `{}` | no |
| <a name="input_input_tags"></a> [input\_tags](#input\_input\_tags) | tags to which pattern/wrapper level information will be merged | `map(string)` | n/a | yes |
| <a name="input_module_name"></a> [module\_name](#input\_module\_name) | name of the wrapper/pattern | `string` | n/a | yes |
| <a name="input_module_version"></a> [module\_version](#input\_module\_version) | version of the wrapper/pattern | `string` | n/a | yes |

## Outputs

| Name | Description |
|------|-------------|
| <a name="output_tags"></a> [tags](#output\_tags) | Final tags with wrapper/pattern level information |
<!-- END_TF_DOCS -->
