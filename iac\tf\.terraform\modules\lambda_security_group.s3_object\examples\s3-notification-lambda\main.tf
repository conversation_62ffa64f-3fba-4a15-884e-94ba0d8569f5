data "aws_vpc" "this" {
  id = var.vpc_id
}

data "aws_region" "current" {}

data "aws_partition" "current" {}
###############################
# Labels Module               #
###############################
module "labels" {
  source  = "artifacts.merck.com/terraform-iac-shared__internal/labels/aws"
  version = "~> 4.1"
  enabled = true

  appname     = var.resource_vars.appname
  region      = var.resource_vars.region
  attributes  = var.resource_vars.attributes
  label_order = var.resource_vars.label_order
  environment = var.resource_vars.environment
}

################################
# S3 Notification with Lambda  #
################################
module "s3_notification" {
  # Change the below source to the proper latest wrapper release found @ https://go.merck.com/iacreleases
  # IMPORTANT: for S3 notification you will need to reference the notification wrapper-module specifically
  ### Example: source = "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-s3-bucket-wrapper_1.6.0.tgz//wrapper-modules/notification"
  source = "../../modules/notification"
  bucket = module.s3_bucket.s3_bucket_id
  lambda_notifications = {
    lambda1 = {
      function_name = module.lambda.lambda_function_name
      function_arn  = module.lambda.lambda_function_arn
      events        = ["s3:ObjectCreated:*"]
    }
  }
  create_sns_policy = false # This example is for Lambda so no need to create a SNS policy
  create_sqs_policy = false # This example is for Lambda so no need to create a SQS policy
}

#########################################
# S3 Bucket (to add notification to)    #
# Disregard if you have existing bucket #
#########################################
module "s3_bucket" {
  # Change the below source to the proper latest wrapper release found @ https://go.merck.com/iacreleases
  source  = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/s3-bucket/aws"
  version = "3.3.0"

  create_bucket                            = true
  bucket                                   = "${module.labels.id}-bucket"
  policy                                   = data.aws_iam_policy_document.s3_bucket_policy.json
  cors_rule                                = var.s3_bucket_cors_rule
  lifecycle_rule                           = var.s3_bucket_lifecycle_rule
  intelligent_tiering                      = var.s3_bucket_intelligent_tiering
  tags                                     = var.tags
  default_tags                             = var.default_tags
  attach_deny_incorrect_encryption_headers = var.attach_deny_incorrect_encryption_headers
  attach_deny_incorrect_kms_key_sse        = var.attach_deny_incorrect_kms_key_sse
  allowed_kms_key_arn                      = var.allowed_kms_key_arn
  attach_deny_unencrypted_object_uploads   = var.attach_deny_unencrypted_object_uploads
  versioning                               = var.s3_bucket_versioning
  attach_policy                            = var.attach_elb_log_delivery_policy || var.attach_lb_log_delivery_policy || var.attach_policy
  website                                  = var.website
  kms_key_id                               = (var.kms_key_id != "" ? var.kms_key_id : module.aws_kms_key_s3_bucket.key_arn)
  sse_algorithm                            = var.sse_algorithm
  bucket_key_enabled                       = true
  replication_configuration                = {}
  force_destroy                            = true # for testing purposes
  s3_log_bucket                            = "${module.labels.id}-log-bucket"
  s3_log_force_destroy                     = true # for testing purposes
}

########## KMS Key - S3 Bucket ###########
module "aws_kms_key_s3_bucket" {
  source       = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/kms/aws"
  version      = "~> 2.5"
  description  = "CMK for primary region"
  tags         = var.tags
  default_tags = var.default_tags

  policy             = data.aws_iam_policy_document.kms_default_primary.json
  aliases            = ["${module.labels.id}-kms-s3-bucket"]
  key_administrators = ["arn:${data.aws_partition.current.partition}:iam::${var.account_no}:root", "arn:${data.aws_partition.current.partition}:iam::${var.account_no}:role/${var.deployment_role}"]
}

########## IAM Policy - S3 Bucket ###########
data "aws_iam_policy_document" "s3_bucket_policy" {
  statement {
    effect = "Allow"
    actions = [
      "s3:ListBucket",
      "lambda:*"
    ]
    resources = ["arn:${data.aws_partition.current.partition}:lambda:${var.region}:${var.account_no}:*"]
  }
}

################################
# Supporting Resources         #
################################

########## Lambda Wrapper ###########
module "lambda" {
  source                            = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/lambda/aws"
  version                           = "~> 4.3"
  function_name                     = "${module.labels.id}-test"
  description                       = var.lambda_authorizer_description
  role_name                         = "${module.labels.id}-test"
  role_description                  = "Lambda Authorizor Role"
  role_tags                         = var.default_tags
  kms_key_arn                       = module.aws_kms_key_lambda.key_arn
  handler                           = var.authorizer_handler
  runtime                           = var.authorizer_runtime
  cloudwatch_logs_kms_key_id        = module.aws_kms_key_lambda.key_arn
  cloudwatch_logs_retention_in_days = var.cloudwatch_logs_retention_in_days
  cloudwatch_logs_tags              = var.default_tags
  publish                           = var.authorizer_publish
  tags                              = var.tags
  default_tags                      = var.default_tags
  create_package                    = var.authorizer_create_package
  local_existing_package            = var.authorizer_local_existing_package
  attach_network_policy             = true
  vpc_subnet_ids                    = var.subnet_ids
  vpc_security_group_ids            = [module.lambda_security_group.security_group_id]

}

########## Security Group - Lambda ###########
module "lambda_security_group" {
  source      = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/security-group/aws"
  version     = "~> 2.5"
  name        = "${module.labels.id}-lambda-sg"
  description = "Security group which is used for a lambda module"
  vpc_id      = var.vpc_id
  ingress_with_cidr_blocks = [
    {
      from_port   = 443
      to_port     = 443
      protocol    = "tcp"
      description = "HTTPS"
      cidr_blocks = data.aws_vpc.this.cidr_block
    }
  ]
  tags         = var.tags
  default_tags = var.default_tags
}

########## ClouWatch Log Group - Lambda ###########
module "cloudwatch_log_group" {
  # Update this source to use the Artifactory URL of a released version of the module: https://go.merck.com/iacreleases
  source       = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/cloudwatch/aws//modules/log-group/"
  version      = "~> 2.2"
  name         = module.labels.id
  kms_key_id   = module.aws_kms_key_lambda.key_arn
  tags         = var.tags
  default_tags = var.default_tags
  depends_on = [
    module.aws_kms_key_lambda
  ]
}

########## KMS Key - Lambda ###########
module "aws_kms_key_lambda" {
  source       = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/kms/aws"
  version      = "~> 2.5"
  description  = "CMK for primary region"
  tags         = var.tags
  default_tags = var.default_tags

  policy             = data.aws_iam_policy_document.kms_default_primary.json
  aliases            = ["${module.labels.id}-kms-lambda"]
  key_administrators = ["arn:${data.aws_partition.current.partition}:iam::${var.account_no}:root", "arn:${data.aws_partition.current.partition}:iam::${var.account_no}:role/${var.deployment_role}"]
}
