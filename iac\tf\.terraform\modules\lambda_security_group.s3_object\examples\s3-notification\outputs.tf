# Outputs
########## S3 Notification ##########
output "s3_bucket_notification_id" {
  description = "The key of S3 object"
  value       = module.s3_notification.s3_bucket_notification_id
}

########## S3 Bucket ##########
output "s3_bucket_id" {
  description = "The name of the bucket."
  value       = module.s3_bucket.s3_bucket_id
}

output "s3_bucket_arn" {
  description = "The arn of the bucket."
  value       = module.s3_bucket.s3_bucket_arn
}

########## S3 Log Bucket ##########
output "log_bucket_id" {
  description = "The name off the log bucket created."
  value       = module.s3_bucket.s3_log_bucket_id
}

output "log_bucket_arn" {
  description = "The arn of the log bucket."
  value       = module.s3_bucket.s3_log_bucket_arn
}

########## KMS Key for S3 Bucket ##########
output "key_arn" {
  description = "The Amazon Resource Name (ARN) of the key"
  value       = module.aws_kms_key_s3_bucket.key_arn
}

output "key_id" {
  description = "The globally unique identifier for the key"
  value       = module.aws_kms_key_s3_bucket.key_id
}

output "key_policy" {
  description = "The IAM resource policy set on the key"
  value       = module.aws_kms_key_s3_bucket.key_policy
}

########## SNS Topic ##########
output "topic_arn" {
  description = "The ARN of the SNS topic, as a more obvious property (clone of id)"
  value       = module.sns_topic.topic_arn
}

output "topic_name" {
  description = "The ARN of the SNS topic, as a more obvious property (clone of id)"
  value       = module.sns_topic.topic_name
}