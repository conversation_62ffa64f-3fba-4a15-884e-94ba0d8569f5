######################################################################################
# Required "User Provided" Variables
# you must modify these with application specific information
######################################################################################

# Labels - this will create consistent naming and tagging across all resources
# All labels become part of the AWS resource name based on label_order
resource_vars = {
  appname     = "tft"
  region      = "cn-north-1"
  attributes  = ["wrap", "sns"]
  environment = "dev"
  label_order = ["appname", "environment"]
}

# Tags - the following tags will be added across all resources
default_tags = {
  Application        = "tft"
  Consumer           = "<EMAIL>"
  Environment        = "Development"
  DataClassification = "Proprietary"
  Service            = "TFT-Development"
}

# AWS Account Information
account_no      = "************"
deployment_role = "deployment-role"
region          = "cn-north-1"
partition       = "aws-cn"

######################################################################################
# Optional "User Provided" Variables
# you can modify the values below to suit your application as needed
######################################################################################

########## DynamoDB Table ###########
subscriptions = {
  key_name = {
    endpoint  = "<EMAIL>", #Required. Endpoint to send data to. Contents vary with the protocol.
    protocol  = "email",                       #Required. Valid values are: sqs, sms, lambda, firehose, application, email, email-json, http, https
    topic_arn = "",                            #Provide name of topic to which subscription needs to be associated with.
  }
} #Configuration block for SNS Topic Subcription. Keep empty if not required {}

other_subscriptions = {
  key_name = {
    endpoint  = "<EMAIL>", #Required. Endpoint to send data to. Contents vary with the protocol.
    protocol  = "email",                       #Required. Valid values are: sqs, sms, lambda, firehose, application, email, email-json, http, https
    topic_arn = "",                            #Provide name of topic to which subscription needs to be associated with.
  }
}
