{"Modules": [{"Key": "", "Source": "", "Dir": "."}, {"Key": "cron", "Source": "artifacts.merck.com/terraform-iac-shared__patterns/generic/aws", "Version": "3.1.1", "Dir": ".terraform/modules/cron"}, {"Key": "cron.alb", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/alb/aws", "Version": "3.1.0", "Dir": ".terraform/modules/cron.alb"}, {"Key": "cron.alb.ext", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/alb/aws", "Version": "9.11.1", "Dir": ".terraform/modules/cron.alb.ext"}, {"Key": "cron.alb.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.alb.tags"}, {"Key": "cron.alb_lb_trust_store", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/alb/aws//modules/lb_trust_store", "Version": "3.1.0", "Dir": ".terraform/modules/cron.alb_lb_trust_store/modules/lb_trust_store"}, {"Key": "cron.alb_lb_trust_store.lb_trust_store", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/alb/aws//modules/lb_trust_store", "Version": "9.11.1", "Dir": ".terraform/modules/cron.alb_lb_trust_store.lb_trust_store/modules/lb_trust_store"}, {"Key": "cron.alb_lb_trust_store.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.alb_lb_trust_store.tags"}, {"Key": "cron.apigateway_v2", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/apigateway-v2/aws", "Version": "3.0.0", "Dir": ".terraform/modules/cron.apigateway_v2"}, {"Key": "cron.apigateway_v2.api_gateway_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/apigateway-v2/aws", "Version": "5.2.1", "Dir": ".terraform/modules/cron.apigateway_v2.api_gateway_wrapper"}, {"Key": "cron.apigateway_v2.api_gateway_wrapper.acm", "Source": "registry.terraform.io/terraform-aws-modules/acm/aws", "Version": "5.0.1", "Dir": ".terraform/modules/cron.apigateway_v2.api_gateway_wrapper.acm"}, {"Key": "cron.apigateway_v2.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.apigateway_v2.tags"}, {"Key": "cron.dynamodb", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/dynamodb-table/aws", "Version": "2.4.0", "Dir": ".terraform/modules/cron.dynamodb"}, {"Key": "cron.dynamodb.dynamodb_table_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/dynamodb-table/aws", "Version": "4.2.0", "Dir": ".terraform/modules/cron.dynamodb.dynamodb_table_wrapper"}, {"Key": "cron.dynamodb.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.dynamodb.tags"}, {"Key": "cron.ec2_autoscaling", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/autoscaling/aws", "Version": "3.0.0", "Dir": ".terraform/modules/cron.ec2_autoscaling"}, {"Key": "cron.ec2_autoscaling.asg_wrapper", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/ext/iac-shared-tf-module-aws-autoscaling-ext_8.0.0-ext.tgz", "Dir": ".terraform/modules/cron.ec2_autoscaling.asg_wrapper"}, {"Key": "cron.ec2_autoscaling.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.ec2_autoscaling.tags"}, {"Key": "cron.ec2_autoscaling_security_group", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/security-group/aws", "Version": "2.5.0", "Dir": ".terraform/modules/cron.ec2_autoscaling_security_group"}, {"Key": "cron.ec2_autoscaling_security_group.security_group_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/security-group/aws", "Version": "5.2.0", "Dir": ".terraform/modules/cron.ec2_autoscaling_security_group.security_group_wrapper"}, {"Key": "cron.ec2_autoscaling_security_group.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.ec2_autoscaling_security_group.tags"}, {"Key": "cron.ec2_instance", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/ec2-instance/aws", "Version": "2.4.0", "Dir": ".terraform/modules/cron.ec2_instance"}, {"Key": "cron.ec2_instance.ec2_instance_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/ec2-instance/aws", "Version": "5.7.1", "Dir": ".terraform/modules/cron.ec2_instance.ec2_instance_wrapper"}, {"Key": "cron.ec2_instance.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.ec2_instance.tags"}, {"Key": "cron.ec2_instance_security_group", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/security-group/aws", "Version": "2.5.0", "Dir": ".terraform/modules/cron.ec2_instance_security_group"}, {"Key": "cron.ec2_instance_security_group.security_group_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/security-group/aws", "Version": "5.2.0", "Dir": ".terraform/modules/cron.ec2_instance_security_group.security_group_wrapper"}, {"Key": "cron.ec2_instance_security_group.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.ec2_instance_security_group.tags"}, {"Key": "cron.ecs", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/ecs/aws", "Version": "3.1.0", "Dir": ".terraform/modules/cron.ecs"}, {"Key": "cron.ecs.ecs_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/ecs/aws", "Version": "5.12.0", "Dir": ".terraform/modules/cron.ecs.ecs_wrapper"}, {"Key": "cron.ecs.ecs_wrapper.cluster", "Source": "./modules/cluster", "Dir": ".terraform/modules/cron.ecs.ecs_wrapper/modules/cluster"}, {"Key": "cron.ecs.ecs_wrapper.service", "Source": "./modules/service", "Dir": ".terraform/modules/cron.ecs.ecs_wrapper/modules/service"}, {"Key": "cron.ecs.ecs_wrapper.service.container_definition", "Source": "../container-definition", "Dir": ".terraform/modules/cron.ecs.ecs_wrapper/modules/container-definition"}, {"Key": "cron.ecs.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.ecs.tags"}, {"Key": "cron.ecs_cluster", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/ecs/aws//modules/cluster", "Version": "3.1.0", "Dir": ".terraform/modules/cron.ecs_cluster/modules/cluster"}, {"Key": "cron.ecs_cluster.cluster", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/ecs/aws//modules/cluster", "Version": "5.12.0", "Dir": ".terraform/modules/cron.ecs_cluster.cluster/modules/cluster"}, {"Key": "cron.ecs_cluster.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.ecs_cluster.tags"}, {"Key": "cron.ecs_container_definition", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/ecs/aws//modules/container-definition", "Version": "3.1.0", "Dir": ".terraform/modules/cron.ecs_container_definition/modules/container-definition"}, {"Key": "cron.ecs_container_definition.container_definition", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/ecs/aws//modules/container-definition", "Version": "5.12.0", "Dir": ".terraform/modules/cron.ecs_container_definition.container_definition/modules/container-definition"}, {"Key": "cron.ecs_container_definition.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.ecs_container_definition.tags"}, {"Key": "cron.ecs_service", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/ecs/aws//modules/service", "Version": "3.1.0", "Dir": ".terraform/modules/cron.ecs_service/modules/service"}, {"Key": "cron.ecs_service.service", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/ecs/aws//modules/service", "Version": "5.12.0", "Dir": ".terraform/modules/cron.ecs_service.service/modules/service"}, {"Key": "cron.ecs_service.service.container_definition", "Source": "../container-definition", "Dir": ".terraform/modules/cron.ecs_service.service/modules/container-definition"}, {"Key": "cron.ecs_service.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.ecs_service.tags"}, {"Key": "cron.efs", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/efs/aws", "Version": "1.7.2", "Dir": ".terraform/modules/cron.efs"}, {"Key": "cron.efs.ext", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/efs/aws", "Version": "1.6.5", "Dir": ".terraform/modules/cron.efs.ext"}, {"Key": "cron.efs.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.efs.tags"}, {"Key": "cron.eventbridge", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/eventbridge/aws", "Version": "0.9.0", "Dir": ".terraform/modules/cron.eventbridge"}, {"Key": "cron.eventbridge.eventbridge_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/eventbridge/aws", "Version": "3.14.2", "Dir": ".terraform/modules/cron.eventbridge.eventbridge_wrapper"}, {"Key": "cron.eventbridge.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.eventbridge.tags"}, {"Key": "cron.kms", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/kms/aws", "Version": "2.5.0", "Dir": ".terraform/modules/cron.kms"}, {"Key": "cron.kms.kms_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/kms/aws", "Version": "3.1.0", "Dir": ".terraform/modules/cron.kms.kms_wrapper"}, {"Key": "cron.kms.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.kms.tags"}, {"Key": "cron.lambda", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/lambda/aws", "Version": "4.3.0", "Dir": ".terraform/modules/cron.lambda"}, {"Key": "cron.lambda.lambda_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/lambda/aws", "Version": "7.16.0", "Dir": ".terraform/modules/cron.lambda.lambda_wrapper"}, {"Key": "cron.lambda.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.lambda.tags"}, {"Key": "cron.lambda_security_group", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/security-group/aws", "Version": "2.5.0", "Dir": ".terraform/modules/cron.lambda_security_group"}, {"Key": "cron.lambda_security_group.security_group_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/security-group/aws", "Version": "5.2.0", "Dir": ".terraform/modules/cron.lambda_security_group.security_group_wrapper"}, {"Key": "cron.lambda_security_group.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.lambda_security_group.tags"}, {"Key": "cron.merck_acm", "Source": "artifacts.merck.com/terraform-iac-shared__cloudposse/private-acm/aws", "Version": "0.4.1", "Dir": ".terraform/modules/cron.merck_acm"}, {"Key": "cron.merck_acm.acm_wrapper", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/ext/iac-shared-tf-module-aws-acm-request-certificate-cp-ext_0.18.0-ext.tgz", "Dir": ".terraform/modules/cron.merck_acm.acm_wrapper"}, {"Key": "cron.merck_acm.acm_wrapper.this", "Source": "registry.terraform.io/cloudposse/label/null", "Version": "0.25.0", "Dir": ".terraform/modules/cron.merck_acm.acm_wrapper.this"}, {"Key": "cron.merck_acm.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.merck_acm.tags"}, {"Key": "cron.merck_route53", "Source": "artifacts.merck.com/terraform-iac-shared__internal/route53/aws", "Version": "0.2.0", "Dir": ".terraform/modules/cron.merck_route53"}, {"Key": "cron.merck_route53.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.merck_route53.tags"}, {"Key": "cron.rds", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/rds/aws", "Version": "5.3.0", "Dir": ".terraform/modules/cron.rds"}, {"Key": "cron.rds.rds_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/rds/aws", "Version": "6.10.0", "Dir": ".terraform/modules/cron.rds.rds_wrapper"}, {"Key": "cron.rds.rds_wrapper.db_instance", "Source": "./modules/db_instance", "Dir": ".terraform/modules/cron.rds.rds_wrapper/modules/db_instance"}, {"Key": "cron.rds.rds_wrapper.db_instance_role_association", "Source": "./modules/db_instance_role_association", "Dir": ".terraform/modules/cron.rds.rds_wrapper/modules/db_instance_role_association"}, {"Key": "cron.rds.rds_wrapper.db_option_group", "Source": "./modules/db_option_group", "Dir": ".terraform/modules/cron.rds.rds_wrapper/modules/db_option_group"}, {"Key": "cron.rds.rds_wrapper.db_parameter_group", "Source": "./modules/db_parameter_group", "Dir": ".terraform/modules/cron.rds.rds_wrapper/modules/db_parameter_group"}, {"Key": "cron.rds.rds_wrapper.db_subnet_group", "Source": "./modules/db_subnet_group", "Dir": ".terraform/modules/cron.rds.rds_wrapper/modules/db_subnet_group"}, {"Key": "cron.rds.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.rds.tags"}, {"Key": "cron.rds_aurora", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/rds-aurora/aws", "Version": "1.9.0", "Dir": ".terraform/modules/cron.rds_aurora"}, {"Key": "cron.rds_aurora.ext", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/rds-aurora/aws", "Version": "9.11.0", "Dir": ".terraform/modules/cron.rds_aurora.ext"}, {"Key": "cron.rds_aurora.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.rds_aurora.tags"}, {"Key": "cron.rds_db_instance", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/rds/aws//modules/db_instance", "Version": "5.3.0", "Dir": ".terraform/modules/cron.rds_db_instance/modules/db_instance"}, {"Key": "cron.rds_db_instance.db_instance", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/rds/aws//modules/db_instance", "Version": "6.10.0", "Dir": ".terraform/modules/cron.rds_db_instance.db_instance/modules/db_instance"}, {"Key": "cron.rds_db_instance.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.rds_db_instance.tags"}, {"Key": "cron.rds_db_instance_automated_backups_replication", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/rds/aws//modules/db_instance_automated_backups_replication", "Version": "5.3.0", "Dir": ".terraform/modules/cron.rds_db_instance_automated_backups_replication/modules/db_instance_automated_backups_replication"}, {"Key": "cron.rds_db_instance_automated_backups_replication.db_instance_automated_backups_replication", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/rds/aws//modules/db_instance_automated_backups_replication", "Version": "6.10.0", "Dir": ".terraform/modules/cron.rds_db_instance_automated_backups_replication.db_instance_automated_backups_replication/modules/db_instance_automated_backups_replication"}, {"Key": "cron.rds_db_option_group", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/rds/aws//modules/db_option_group", "Version": "5.3.0", "Dir": ".terraform/modules/cron.rds_db_option_group/modules/db_option_group"}, {"Key": "cron.rds_db_option_group.db_option_group", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/rds/aws//modules/db_option_group", "Version": "6.10.0", "Dir": ".terraform/modules/cron.rds_db_option_group.db_option_group/modules/db_option_group"}, {"Key": "cron.rds_db_option_group.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.rds_db_option_group.tags"}, {"Key": "cron.rds_db_parameter_group", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/rds/aws//modules/db_parameter_group", "Version": "5.3.0", "Dir": ".terraform/modules/cron.rds_db_parameter_group/modules/db_parameter_group"}, {"Key": "cron.rds_db_parameter_group.db_parameter_group", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/rds/aws//modules/db_parameter_group", "Version": "6.10.0", "Dir": ".terraform/modules/cron.rds_db_parameter_group.db_parameter_group/modules/db_parameter_group"}, {"Key": "cron.rds_db_parameter_group.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.rds_db_parameter_group.tags"}, {"Key": "cron.rds_db_subnet_group", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/rds/aws//modules/db_subnet_group", "Version": "5.3.0", "Dir": ".terraform/modules/cron.rds_db_subnet_group/modules/db_subnet_group"}, {"Key": "cron.rds_db_subnet_group.db_subnet_group", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/rds/aws//modules/db_subnet_group", "Version": "6.10.0", "Dir": ".terraform/modules/cron.rds_db_subnet_group.db_subnet_group/modules/db_subnet_group"}, {"Key": "cron.rds_db_subnet_group.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.rds_db_subnet_group.tags"}, {"Key": "cron.rds_security_group", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/security-group/aws", "Version": "2.5.0", "Dir": ".terraform/modules/cron.rds_security_group"}, {"Key": "cron.rds_security_group.security_group_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/security-group/aws", "Version": "5.2.0", "Dir": ".terraform/modules/cron.rds_security_group.security_group_wrapper"}, {"Key": "cron.rds_security_group.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.rds_security_group.tags"}, {"Key": "cron.s3", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/s3-bucket/aws", "Version": "3.3.0", "Dir": ".terraform/modules/cron.s3"}, {"Key": "cron.s3.s3_bucket_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/s3-bucket/aws", "Version": "4.2.2", "Dir": ".terraform/modules/cron.s3.s3_bucket_wrapper"}, {"Key": "cron.s3.s3_log_bucket", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/s3-bucket/aws", "Version": "4.2.2", "Dir": ".terraform/modules/cron.s3.s3_log_bucket"}, {"Key": "cron.s3.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.s3.tags"}, {"Key": "cron.s3_notification", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/s3-bucket/aws//modules/notification", "Version": "3.3.0", "Dir": ".terraform/modules/cron.s3_notification/modules/notification"}, {"Key": "cron.s3_notification.notification", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/s3-bucket/aws//modules/notification", "Version": "4.2.2", "Dir": ".terraform/modules/cron.s3_notification.notification/modules/notification"}, {"Key": "cron.s3_object", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/s3-bucket/aws//modules/object", "Version": "3.3.0", "Dir": ".terraform/modules/cron.s3_object/modules/object"}, {"Key": "cron.s3_object.object", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/s3-bucket/aws//modules/object", "Version": "4.2.2", "Dir": ".terraform/modules/cron.s3_object.object/modules/object"}, {"Key": "cron.sns", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/sns/aws", "Version": "2.2.1", "Dir": ".terraform/modules/cron.sns"}, {"Key": "cron.sns.ext", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/sns/aws", "Version": "6.1.2", "Dir": ".terraform/modules/cron.sns.ext"}, {"Key": "cron.sns.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.sns.tags"}, {"Key": "cron.sqs", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/sqs/aws", "Version": "1.4.1", "Dir": ".terraform/modules/cron.sqs"}, {"Key": "cron.sqs.sqs_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/sqs/aws", "Version": "4.2.1", "Dir": ".terraform/modules/cron.sqs.sqs_wrapper"}, {"Key": "cron.sqs.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/cron.sqs.tags"}, {"Key": "cron.tags", "Source": "artifacts.merck.com/terraform-iac-shared__internal/adoption-tags/aws", "Version": "1.0.0", "Dir": ".terraform/modules/cron.tags"}, {"Key": "labels", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-labels-int_4.0.1.tgz", "Dir": ".terraform/modules/labels"}, {"Key": "lambda_security_group", "Source": "artifacts.merck.com/terraform-iac-shared__patterns/generic/aws", "Version": "3.1.1", "Dir": ".terraform/modules/lambda_security_group"}, {"Key": "lambda_security_group.alb", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/alb/aws", "Version": "3.1.0", "Dir": ".terraform/modules/lambda_security_group.alb"}, {"Key": "lambda_security_group.alb.ext", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/alb/aws", "Version": "9.11.1", "Dir": ".terraform/modules/lambda_security_group.alb.ext"}, {"Key": "lambda_security_group.alb.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.alb.tags"}, {"Key": "lambda_security_group.alb_lb_trust_store", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/alb/aws//modules/lb_trust_store", "Version": "3.1.0", "Dir": ".terraform/modules/lambda_security_group.alb_lb_trust_store/modules/lb_trust_store"}, {"Key": "lambda_security_group.alb_lb_trust_store.lb_trust_store", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/alb/aws//modules/lb_trust_store", "Version": "9.11.1", "Dir": ".terraform/modules/lambda_security_group.alb_lb_trust_store.lb_trust_store/modules/lb_trust_store"}, {"Key": "lambda_security_group.alb_lb_trust_store.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.alb_lb_trust_store.tags"}, {"Key": "lambda_security_group.apigateway_v2", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/apigateway-v2/aws", "Version": "3.0.0", "Dir": ".terraform/modules/lambda_security_group.apigateway_v2"}, {"Key": "lambda_security_group.apigateway_v2.api_gateway_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/apigateway-v2/aws", "Version": "5.2.1", "Dir": ".terraform/modules/lambda_security_group.apigateway_v2.api_gateway_wrapper"}, {"Key": "lambda_security_group.apigateway_v2.api_gateway_wrapper.acm", "Source": "registry.terraform.io/terraform-aws-modules/acm/aws", "Version": "5.0.1", "Dir": ".terraform/modules/lambda_security_group.apigateway_v2.api_gateway_wrapper.acm"}, {"Key": "lambda_security_group.apigateway_v2.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.apigateway_v2.tags"}, {"Key": "lambda_security_group.dynamodb", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/dynamodb-table/aws", "Version": "2.4.0", "Dir": ".terraform/modules/lambda_security_group.dynamodb"}, {"Key": "lambda_security_group.dynamodb.dynamodb_table_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/dynamodb-table/aws", "Version": "4.2.0", "Dir": ".terraform/modules/lambda_security_group.dynamodb.dynamodb_table_wrapper"}, {"Key": "lambda_security_group.dynamodb.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.dynamodb.tags"}, {"Key": "lambda_security_group.ec2_autoscaling", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/autoscaling/aws", "Version": "3.0.0", "Dir": ".terraform/modules/lambda_security_group.ec2_autoscaling"}, {"Key": "lambda_security_group.ec2_autoscaling.asg_wrapper", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/ext/iac-shared-tf-module-aws-autoscaling-ext_8.0.0-ext.tgz", "Dir": ".terraform/modules/lambda_security_group.ec2_autoscaling.asg_wrapper"}, {"Key": "lambda_security_group.ec2_autoscaling.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.ec2_autoscaling.tags"}, {"Key": "lambda_security_group.ec2_autoscaling_security_group", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/security-group/aws", "Version": "2.5.0", "Dir": ".terraform/modules/lambda_security_group.ec2_autoscaling_security_group"}, {"Key": "lambda_security_group.ec2_autoscaling_security_group.security_group_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/security-group/aws", "Version": "5.2.0", "Dir": ".terraform/modules/lambda_security_group.ec2_autoscaling_security_group.security_group_wrapper"}, {"Key": "lambda_security_group.ec2_autoscaling_security_group.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.ec2_autoscaling_security_group.tags"}, {"Key": "lambda_security_group.ec2_instance", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/ec2-instance/aws", "Version": "2.4.0", "Dir": ".terraform/modules/lambda_security_group.ec2_instance"}, {"Key": "lambda_security_group.ec2_instance.ec2_instance_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/ec2-instance/aws", "Version": "5.7.1", "Dir": ".terraform/modules/lambda_security_group.ec2_instance.ec2_instance_wrapper"}, {"Key": "lambda_security_group.ec2_instance.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.ec2_instance.tags"}, {"Key": "lambda_security_group.ec2_instance_security_group", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/security-group/aws", "Version": "2.5.0", "Dir": ".terraform/modules/lambda_security_group.ec2_instance_security_group"}, {"Key": "lambda_security_group.ec2_instance_security_group.security_group_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/security-group/aws", "Version": "5.2.0", "Dir": ".terraform/modules/lambda_security_group.ec2_instance_security_group.security_group_wrapper"}, {"Key": "lambda_security_group.ec2_instance_security_group.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.ec2_instance_security_group.tags"}, {"Key": "lambda_security_group.ecs", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/ecs/aws", "Version": "3.1.0", "Dir": ".terraform/modules/lambda_security_group.ecs"}, {"Key": "lambda_security_group.ecs.ecs_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/ecs/aws", "Version": "5.12.0", "Dir": ".terraform/modules/lambda_security_group.ecs.ecs_wrapper"}, {"Key": "lambda_security_group.ecs.ecs_wrapper.cluster", "Source": "./modules/cluster", "Dir": ".terraform/modules/lambda_security_group.ecs.ecs_wrapper/modules/cluster"}, {"Key": "lambda_security_group.ecs.ecs_wrapper.service", "Source": "./modules/service", "Dir": ".terraform/modules/lambda_security_group.ecs.ecs_wrapper/modules/service"}, {"Key": "lambda_security_group.ecs.ecs_wrapper.service.container_definition", "Source": "../container-definition", "Dir": ".terraform/modules/lambda_security_group.ecs.ecs_wrapper/modules/container-definition"}, {"Key": "lambda_security_group.ecs.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.ecs.tags"}, {"Key": "lambda_security_group.ecs_cluster", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/ecs/aws//modules/cluster", "Version": "3.1.0", "Dir": ".terraform/modules/lambda_security_group.ecs_cluster/modules/cluster"}, {"Key": "lambda_security_group.ecs_cluster.cluster", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/ecs/aws//modules/cluster", "Version": "5.12.0", "Dir": ".terraform/modules/lambda_security_group.ecs_cluster.cluster/modules/cluster"}, {"Key": "lambda_security_group.ecs_cluster.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.ecs_cluster.tags"}, {"Key": "lambda_security_group.ecs_container_definition", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/ecs/aws//modules/container-definition", "Version": "3.1.0", "Dir": ".terraform/modules/lambda_security_group.ecs_container_definition/modules/container-definition"}, {"Key": "lambda_security_group.ecs_container_definition.container_definition", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/ecs/aws//modules/container-definition", "Version": "5.12.0", "Dir": ".terraform/modules/lambda_security_group.ecs_container_definition.container_definition/modules/container-definition"}, {"Key": "lambda_security_group.ecs_container_definition.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.ecs_container_definition.tags"}, {"Key": "lambda_security_group.ecs_service", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/ecs/aws//modules/service", "Version": "3.1.0", "Dir": ".terraform/modules/lambda_security_group.ecs_service/modules/service"}, {"Key": "lambda_security_group.ecs_service.service", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/ecs/aws//modules/service", "Version": "5.12.0", "Dir": ".terraform/modules/lambda_security_group.ecs_service.service/modules/service"}, {"Key": "lambda_security_group.ecs_service.service.container_definition", "Source": "../container-definition", "Dir": ".terraform/modules/lambda_security_group.ecs_service.service/modules/container-definition"}, {"Key": "lambda_security_group.ecs_service.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.ecs_service.tags"}, {"Key": "lambda_security_group.efs", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/efs/aws", "Version": "1.7.2", "Dir": ".terraform/modules/lambda_security_group.efs"}, {"Key": "lambda_security_group.efs.ext", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/efs/aws", "Version": "1.6.5", "Dir": ".terraform/modules/lambda_security_group.efs.ext"}, {"Key": "lambda_security_group.efs.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.efs.tags"}, {"Key": "lambda_security_group.eventbridge", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/eventbridge/aws", "Version": "0.9.0", "Dir": ".terraform/modules/lambda_security_group.eventbridge"}, {"Key": "lambda_security_group.eventbridge.eventbridge_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/eventbridge/aws", "Version": "3.14.2", "Dir": ".terraform/modules/lambda_security_group.eventbridge.eventbridge_wrapper"}, {"Key": "lambda_security_group.eventbridge.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.eventbridge.tags"}, {"Key": "lambda_security_group.kms", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/kms/aws", "Version": "2.5.0", "Dir": ".terraform/modules/lambda_security_group.kms"}, {"Key": "lambda_security_group.kms.kms_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/kms/aws", "Version": "3.1.0", "Dir": ".terraform/modules/lambda_security_group.kms.kms_wrapper"}, {"Key": "lambda_security_group.kms.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.kms.tags"}, {"Key": "lambda_security_group.lambda", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/lambda/aws", "Version": "4.3.0", "Dir": ".terraform/modules/lambda_security_group.lambda"}, {"Key": "lambda_security_group.lambda.lambda_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/lambda/aws", "Version": "7.16.0", "Dir": ".terraform/modules/lambda_security_group.lambda.lambda_wrapper"}, {"Key": "lambda_security_group.lambda.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.lambda.tags"}, {"Key": "lambda_security_group.lambda_security_group", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/security-group/aws", "Version": "2.5.0", "Dir": ".terraform/modules/lambda_security_group.lambda_security_group"}, {"Key": "lambda_security_group.lambda_security_group.security_group_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/security-group/aws", "Version": "5.2.0", "Dir": ".terraform/modules/lambda_security_group.lambda_security_group.security_group_wrapper"}, {"Key": "lambda_security_group.lambda_security_group.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.lambda_security_group.tags"}, {"Key": "lambda_security_group.merck_acm", "Source": "artifacts.merck.com/terraform-iac-shared__cloudposse/private-acm/aws", "Version": "0.4.1", "Dir": ".terraform/modules/lambda_security_group.merck_acm"}, {"Key": "lambda_security_group.merck_acm.acm_wrapper", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/ext/iac-shared-tf-module-aws-acm-request-certificate-cp-ext_0.18.0-ext.tgz", "Dir": ".terraform/modules/lambda_security_group.merck_acm.acm_wrapper"}, {"Key": "lambda_security_group.merck_acm.acm_wrapper.this", "Source": "registry.terraform.io/cloudposse/label/null", "Version": "0.25.0", "Dir": ".terraform/modules/lambda_security_group.merck_acm.acm_wrapper.this"}, {"Key": "lambda_security_group.merck_acm.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.merck_acm.tags"}, {"Key": "lambda_security_group.merck_route53", "Source": "artifacts.merck.com/terraform-iac-shared__internal/route53/aws", "Version": "0.2.0", "Dir": ".terraform/modules/lambda_security_group.merck_route53"}, {"Key": "lambda_security_group.merck_route53.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.merck_route53.tags"}, {"Key": "lambda_security_group.rds", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/rds/aws", "Version": "5.3.0", "Dir": ".terraform/modules/lambda_security_group.rds"}, {"Key": "lambda_security_group.rds.rds_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/rds/aws", "Version": "6.10.0", "Dir": ".terraform/modules/lambda_security_group.rds.rds_wrapper"}, {"Key": "lambda_security_group.rds.rds_wrapper.db_instance", "Source": "./modules/db_instance", "Dir": ".terraform/modules/lambda_security_group.rds.rds_wrapper/modules/db_instance"}, {"Key": "lambda_security_group.rds.rds_wrapper.db_instance_role_association", "Source": "./modules/db_instance_role_association", "Dir": ".terraform/modules/lambda_security_group.rds.rds_wrapper/modules/db_instance_role_association"}, {"Key": "lambda_security_group.rds.rds_wrapper.db_option_group", "Source": "./modules/db_option_group", "Dir": ".terraform/modules/lambda_security_group.rds.rds_wrapper/modules/db_option_group"}, {"Key": "lambda_security_group.rds.rds_wrapper.db_parameter_group", "Source": "./modules/db_parameter_group", "Dir": ".terraform/modules/lambda_security_group.rds.rds_wrapper/modules/db_parameter_group"}, {"Key": "lambda_security_group.rds.rds_wrapper.db_subnet_group", "Source": "./modules/db_subnet_group", "Dir": ".terraform/modules/lambda_security_group.rds.rds_wrapper/modules/db_subnet_group"}, {"Key": "lambda_security_group.rds.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.rds.tags"}, {"Key": "lambda_security_group.rds_aurora", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/rds-aurora/aws", "Version": "1.9.0", "Dir": ".terraform/modules/lambda_security_group.rds_aurora"}, {"Key": "lambda_security_group.rds_aurora.ext", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/rds-aurora/aws", "Version": "9.11.0", "Dir": ".terraform/modules/lambda_security_group.rds_aurora.ext"}, {"Key": "lambda_security_group.rds_aurora.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.rds_aurora.tags"}, {"Key": "lambda_security_group.rds_db_instance", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/rds/aws//modules/db_instance", "Version": "5.3.0", "Dir": ".terraform/modules/lambda_security_group.rds_db_instance/modules/db_instance"}, {"Key": "lambda_security_group.rds_db_instance.db_instance", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/rds/aws//modules/db_instance", "Version": "6.10.0", "Dir": ".terraform/modules/lambda_security_group.rds_db_instance.db_instance/modules/db_instance"}, {"Key": "lambda_security_group.rds_db_instance.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.rds_db_instance.tags"}, {"Key": "lambda_security_group.rds_db_instance_automated_backups_replication", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/rds/aws//modules/db_instance_automated_backups_replication", "Version": "5.3.0", "Dir": ".terraform/modules/lambda_security_group.rds_db_instance_automated_backups_replication/modules/db_instance_automated_backups_replication"}, {"Key": "lambda_security_group.rds_db_instance_automated_backups_replication.db_instance_automated_backups_replication", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/rds/aws//modules/db_instance_automated_backups_replication", "Version": "6.10.0", "Dir": ".terraform/modules/lambda_security_group.rds_db_instance_automated_backups_replication.db_instance_automated_backups_replication/modules/db_instance_automated_backups_replication"}, {"Key": "lambda_security_group.rds_db_option_group", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/rds/aws//modules/db_option_group", "Version": "5.3.0", "Dir": ".terraform/modules/lambda_security_group.rds_db_option_group/modules/db_option_group"}, {"Key": "lambda_security_group.rds_db_option_group.db_option_group", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/rds/aws//modules/db_option_group", "Version": "6.10.0", "Dir": ".terraform/modules/lambda_security_group.rds_db_option_group.db_option_group/modules/db_option_group"}, {"Key": "lambda_security_group.rds_db_option_group.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.rds_db_option_group.tags"}, {"Key": "lambda_security_group.rds_db_parameter_group", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/rds/aws//modules/db_parameter_group", "Version": "5.3.0", "Dir": ".terraform/modules/lambda_security_group.rds_db_parameter_group/modules/db_parameter_group"}, {"Key": "lambda_security_group.rds_db_parameter_group.db_parameter_group", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/rds/aws//modules/db_parameter_group", "Version": "6.10.0", "Dir": ".terraform/modules/lambda_security_group.rds_db_parameter_group.db_parameter_group/modules/db_parameter_group"}, {"Key": "lambda_security_group.rds_db_parameter_group.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.rds_db_parameter_group.tags"}, {"Key": "lambda_security_group.rds_db_subnet_group", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/rds/aws//modules/db_subnet_group", "Version": "5.3.0", "Dir": ".terraform/modules/lambda_security_group.rds_db_subnet_group/modules/db_subnet_group"}, {"Key": "lambda_security_group.rds_db_subnet_group.db_subnet_group", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/rds/aws//modules/db_subnet_group", "Version": "6.10.0", "Dir": ".terraform/modules/lambda_security_group.rds_db_subnet_group.db_subnet_group/modules/db_subnet_group"}, {"Key": "lambda_security_group.rds_db_subnet_group.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.rds_db_subnet_group.tags"}, {"Key": "lambda_security_group.rds_security_group", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/security-group/aws", "Version": "2.5.0", "Dir": ".terraform/modules/lambda_security_group.rds_security_group"}, {"Key": "lambda_security_group.rds_security_group.security_group_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/security-group/aws", "Version": "5.2.0", "Dir": ".terraform/modules/lambda_security_group.rds_security_group.security_group_wrapper"}, {"Key": "lambda_security_group.rds_security_group.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.rds_security_group.tags"}, {"Key": "lambda_security_group.s3", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/s3-bucket/aws", "Version": "3.3.0", "Dir": ".terraform/modules/lambda_security_group.s3"}, {"Key": "lambda_security_group.s3.s3_bucket_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/s3-bucket/aws", "Version": "4.2.2", "Dir": ".terraform/modules/lambda_security_group.s3.s3_bucket_wrapper"}, {"Key": "lambda_security_group.s3.s3_log_bucket", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/s3-bucket/aws", "Version": "4.2.2", "Dir": ".terraform/modules/lambda_security_group.s3.s3_log_bucket"}, {"Key": "lambda_security_group.s3.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.s3.tags"}, {"Key": "lambda_security_group.s3_notification", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/s3-bucket/aws//modules/notification", "Version": "3.3.0", "Dir": ".terraform/modules/lambda_security_group.s3_notification/modules/notification"}, {"Key": "lambda_security_group.s3_notification.notification", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/s3-bucket/aws//modules/notification", "Version": "4.2.2", "Dir": ".terraform/modules/lambda_security_group.s3_notification.notification/modules/notification"}, {"Key": "lambda_security_group.s3_object", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/s3-bucket/aws//modules/object", "Version": "3.3.0", "Dir": ".terraform/modules/lambda_security_group.s3_object/modules/object"}, {"Key": "lambda_security_group.s3_object.object", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/s3-bucket/aws//modules/object", "Version": "4.2.2", "Dir": ".terraform/modules/lambda_security_group.s3_object.object/modules/object"}, {"Key": "lambda_security_group.sns", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/sns/aws", "Version": "2.2.1", "Dir": ".terraform/modules/lambda_security_group.sns"}, {"Key": "lambda_security_group.sns.ext", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/sns/aws", "Version": "6.1.2", "Dir": ".terraform/modules/lambda_security_group.sns.ext"}, {"Key": "lambda_security_group.sns.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.sns.tags"}, {"Key": "lambda_security_group.sqs", "Source": "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/sqs/aws", "Version": "1.4.1", "Dir": ".terraform/modules/lambda_security_group.sqs"}, {"Key": "lambda_security_group.sqs.sqs_wrapper", "Source": "artifacts.merck.com/terraform-iac-shared-ext-modules-local__terraform-aws-modules/sqs/aws", "Version": "4.2.1", "Dir": ".terraform/modules/lambda_security_group.sqs.sqs_wrapper"}, {"Key": "lambda_security_group.sqs.tags", "Source": "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-adoption-tags-int_1.0.0.tgz", "Dir": ".terraform/modules/lambda_security_group.sqs.tags"}, {"Key": "lambda_security_group.tags", "Source": "artifacts.merck.com/terraform-iac-shared__internal/adoption-tags/aws", "Version": "1.0.0", "Dir": ".terraform/modules/lambda_security_group.tags"}]}