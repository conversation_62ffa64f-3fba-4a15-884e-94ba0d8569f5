# Outputs

output "s3_bucket_id" {
  description = "The name of the bucket."
  value       = module.s3_bucket.s3_bucket_id
}

output "s3_bucket_arn" {
  description = "The arn of the bucket."
  value       = module.s3_bucket.s3_bucket_arn
}

output "key_arn" {
  description = "The Amazon Resource Name (ARN) of the key"
  value       = module.aws_kms_key.key_arn
}

output "key_id" {
  description = "The globally unique identifier for the key"
  value       = module.aws_kms_key.key_id
}

output "key_policy" {
  description = "The IAM resource policy set on the key"
  value       = module.aws_kms_key.key_policy
}

output "log_bucket_id" {
  description = "The name off the log bucket created."
  value       = module.s3_bucket.s3_log_bucket_id
}

output "log_bucket_arn" {
  description = "The arn of the log bucket."
  value       = module.s3_bucket.s3_log_bucket_arn
}

