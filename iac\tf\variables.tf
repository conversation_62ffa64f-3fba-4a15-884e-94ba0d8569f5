##############################
# common #
###############################
variable "default_tags" {
  description = "A map of default tags to be applied on each resource. You can get required tags [here](https://cloud.merck.com/documentation/compliance/tagging-standards/index.html)"
  type        = map(string)
  validation {
    condition     = length(setintersection(keys(var.default_tags), ["DataClassification", "Consumer", "Application", "Environment", "Service"])) >= length(["DataClassification", "Consumer", "Application", "Environment", "Service"])
    error_message = "Keys: DataClassification, Consumer, Application, Environment, Service are required!"
  }
}

variable "tags" {
  type        = map(string)
  description = "map of optional/extra tags"
  default     = {}
}

variable "resource_vars" {
  description = "map of tags to pass to labels module"
  type = object({
    appname     = string
    region      = string
    attributes  = list(string)
    label_order = list(string)
    environment = string
  })
}

variable "region" {
  description = "AWS Region"
  type        = string
  default     = "us-east-1"
}


variable "deployment_role" {
  description = "Terraform Deployment Role"
  type        = string
}

variable "account_no" {
  description = "AWS account number to deploy to"
  type        = string
}

variable "vpc_id" {
  description = "VPC ID to deploy to"
  type        = string
}

variable "partition" {
  type        = string
  default     = "aws"
  description = "Second part of aws arn. Used only in the provider configuration."
}

variable "vpc_subnet_ids" {
  type        = list(string)
  description = "List of subnet ids associated with specified VPCs. Leave blank to use defaul Subnet group (if present)"
  default     = []
}



