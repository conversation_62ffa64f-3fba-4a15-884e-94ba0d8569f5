locals {
  tags_context = {
    Application        = var.default_tags.Application
    Environment        = var.default_tags.Environment
    DataClassification = var.default_tags.DataClassification
    Costcenter         = lookup(var.default_tags, "Costcenter", "")
    Division           = lookup(var.default_tags, "Division", "")
    Consumer           = var.default_tags.Consumer
    Service            = var.default_tags.Service
  }

  # mapping the default tags again and clean up the map if no value found
  generated_tags = { for l in keys(local.tags_context) : title(l) => local.tags_context[l] if length(local.tags_context[l]) > 0 }

  # merging the custom input tags given by user and the default tags 
  module_tags = merge(var.input_tags, local.generated_tags)

  #-----------------------------------------------------#
  ################## adoption tags ######################
  #-----------------------------------------------------#


  #--fetching the pattern/module version information from Version file--#
  local_tag_value = trimspace("${var.module_name}=${var.module_version}")

  #--checking if it is a pattern or module--#
  is_pattern = length(regexall("pattern", local.local_tag_value)) > 0 ? true : false


  #--checking if the tags already has pattern or module value in the map--#
  tag_pattern_exists = length(lookup(local.module_tags, "IacPattern", "")) > 0 ? true : false
  tag_module_exists  = length(lookup(local.module_tags, "IacModule", "")) > 0 ? true : false


  # -- Is IacPattern already exists. This means that this is the top most call from the IAC pattern and the tag value should be preserved. if IacPattern doesn't exists. Then it checks if this a pattern by checking the "local_tag_value"
  add_pattern_tag = local.tag_pattern_exists ? local.module_tags : (local.is_pattern ? merge(local.module_tags, { IacPattern = tostring(local.local_tag_value) }) : {})

  # -- If pattern not present then it moves on to check if IacModule is present or not . If Present then no further action otherwise add module tag value.
  add_module_tag = length(local.add_pattern_tag) == 0 ? (local.tag_module_exists ? local.module_tags : merge(local.module_tags, { IacModule = local.local_tag_value })) : {}

  # -- If pattern or module both are not present. Then it adds a standard IaC tag with given module name  
  add_iac_tag = length(local.add_module_tag) == 0 && length(local.add_pattern_tag) == 0 ? merge(local.module_tags, { IaC = local.local_tag_value }) : {}


  tags = merge(local.add_iac_tag, local.add_module_tag, local.add_pattern_tag) #local.adoption_tags
}
