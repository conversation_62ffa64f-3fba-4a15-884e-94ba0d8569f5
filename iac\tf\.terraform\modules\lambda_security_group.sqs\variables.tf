#>>GENERATE_CONTENT

variable "default_tags" {
  type        = map(string)
  description = "A map of default tags to be applied on each resource. You can get required tags [here](https://cloud.merck.com/documentation/compliance/tagging-standards/index.html)"
  validation {
    condition     = length(setintersection(keys(var.default_tags), ["DataClassification", "Consumer", "Application", "Environment", "Service"])) >= length(["DataClassification", "Consumer", "Application", "Environment", "Service"])
    error_message = "Keys: DataClassification, Consumer, Application, Environment, Service are required!"
  }
}

variable "content_based_deduplication" {
  type        = bool
  default     = null
  description = "Enables content-based deduplication for FIFO queues"
}

variable "create" {
  type        = bool
  default     = true
  description = "Whether to create SQS queue"
}

variable "create_dlq" {
  type        = bool
  default     = false
  description = "Determines whether to create SQS dead letter queue"
}

variable "create_dlq_queue_policy" {
  type        = bool
  default     = false
  description = "Whether to create SQS queue policy"
}

variable "create_dlq_redrive_allow_policy" {
  type        = bool
  default     = true
  description = "Determines whether to create a redrive allow policy for the dead letter queue."
}

variable "deduplication_scope" {
  type        = string
  default     = null
  description = "Specifies whether message deduplication occurs at the message group or queue level"
}

variable "delay_seconds" {
  type        = number
  default     = null
  description = "The time in seconds that the delivery of all messages in the queue will be delayed. An integer from 0 to 900 (15 minutes)"
}

variable "dlq_content_based_deduplication" {
  type        = bool
  default     = null
  description = "Enables content-based deduplication for FIFO queues"
}

variable "dlq_deduplication_scope" {
  type        = string
  default     = null
  description = "Specifies whether message deduplication occurs at the message group or queue level"
}

variable "dlq_delay_seconds" {
  type        = number
  default     = null
  description = "The time in seconds that the delivery of all messages in the queue will be delayed. An integer from 0 to 900 (15 minutes)"
}

variable "dlq_kms_data_key_reuse_period_seconds" {
  type        = number
  default     = null
  description = "The length of time, in seconds, for which Amazon SQS can reuse a data key to encrypt or decrypt messages before calling AWS KMS again. An integer representing seconds, between 60 seconds (1 minute) and 86,400 seconds (24 hours)"
}

variable "dlq_kms_master_key_id" {
  type        = string
  default     = null
  description = "The ID of an AWS-managed customer master key (CMK) for Amazon SQS or a custom CMK"
}

variable "dlq_message_retention_seconds" {
  type        = number
  default     = null
  description = "The number of seconds Amazon SQS retains a message. Integer representing seconds, from 60 (1 minute) to 1209600 (14 days)"
}

variable "dlq_name" {
  type        = string
  default     = null
  description = "This is the human-readable name of the queue. If omitted, Terraform will assign a random name"
}

variable "dlq_queue_policy_statements" {
  type        = any
  default     = {}
  description = "A map of IAM policy [statements](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document#statement) for custom permission usage"
}

variable "dlq_receive_wait_time_seconds" {
  type        = number
  default     = null
  description = "The time for which a ReceiveMessage call will wait for a message to arrive (long polling) before returning. An integer from 0 to 20 (seconds)"
}

variable "dlq_redrive_allow_policy" {
  type        = any
  default     = {}
  description = "The JSON policy to set up the Dead Letter Queue redrive permission, see AWS docs."
}

variable "dlq_sqs_managed_sse_enabled" {
  type        = bool
  default     = true
  description = "Boolean to enable server-side encryption (SSE) of message content with SQS-owned encryption keys"
}

variable "dlq_tags" {
  type        = map(string)
  default     = {}
  description = "A mapping of additional tags to assign to the dead letter queue"
}

variable "dlq_visibility_timeout_seconds" {
  type        = number
  default     = null
  description = "The visibility timeout for the queue. An integer from 0 to 43200 (12 hours)"
}

variable "fifo_queue" {
  type        = bool
  default     = false
  description = "Boolean designating a FIFO queue"
}

variable "fifo_throughput_limit" {
  type        = string
  default     = null
  description = "Specifies whether the FIFO queue throughput quota applies to the entire queue or per message group"
}

variable "kms_data_key_reuse_period_seconds" {
  type        = number
  default     = null
  description = "The length of time, in seconds, for which Amazon SQS can reuse a data key to encrypt or decrypt messages before calling AWS KMS again. An integer representing seconds, between 60 seconds (1 minute) and 86,400 seconds (24 hours)"
}

variable "kms_master_key_id" {
  type        = string
  default     = null
  description = "The ID of an AWS-managed customer master key (CMK) for Amazon SQS or a custom CMK"
}

variable "max_message_size" {
  type        = number
  default     = null
  description = "The limit of how many bytes a message can contain before Amazon SQS rejects it. An integer from 1024 bytes (1 KiB) up to 262144 bytes (256 KiB)"
}

variable "message_retention_seconds" {
  type        = number
  default     = null
  description = "The number of seconds Amazon SQS retains a message. Integer representing seconds, from 60 (1 minute) to 1209600 (14 days)"
}

variable "name" {
  type        = string
  default     = null
  description = "This is the human-readable name of the queue. If omitted, Terraform will assign a random name"
}

variable "override_dlq_queue_policy_documents" {
  type        = list(string)
  default     = []
  description = "List of IAM policy documents that are merged together into the exported document. In merging, statements with non-blank `sid`s will override statements with the same `sid`"
}

variable "override_queue_policy_documents" {
  type        = list(string)
  default     = []
  description = "List of IAM policy documents that are merged together into the exported document. In merging, statements with non-blank `sid`s will override statements with the same `sid`"
}

variable "queue_policy_statements" {
  type        = any
  default     = {}
  description = "A map of IAM policy [statements](https://registry.terraform.io/providers/hashicorp/aws/latest/docs/data-sources/iam_policy_document#statement) for custom permission usage"
}

variable "receive_wait_time_seconds" {
  type        = number
  default     = null
  description = "The time for which a ReceiveMessage call will wait for a message to arrive (long polling) before returning. An integer from 0 to 20 (seconds)"
}

variable "redrive_allow_policy" {
  type        = any
  default     = {}
  description = "The JSON policy to set up the Dead Letter Queue redrive permission, see AWS docs."
}

variable "redrive_policy" {
  type        = any
  default     = {}
  description = "The JSON policy to set up the Dead Letter Queue, see AWS docs. Note: when specifying maxReceiveCount, you must specify it as an integer (5), and not a string (\"5\")"
}

variable "source_dlq_queue_policy_documents" {
  type        = list(string)
  default     = []
  description = "List of IAM policy documents that are merged together into the exported document. Statements must have unique `sid`s"
}

variable "source_queue_policy_documents" {
  type        = list(string)
  default     = []
  description = "List of IAM policy documents that are merged together into the exported document. Statements must have unique `sid`s"
}

variable "tags" {
  type        = map(string)
  default     = {}
  description = "A mapping of tags to assign to all resources"
}

variable "use_name_prefix" {
  type        = bool
  default     = false
  description = "Determines whether `name` is used as a prefix"
}

variable "visibility_timeout_seconds" {
  type        = number
  default     = null
  description = "The visibility timeout for the queue. An integer from 0 to 43200 (12 hours)"
}
#>>GENERATE_CONTENT
