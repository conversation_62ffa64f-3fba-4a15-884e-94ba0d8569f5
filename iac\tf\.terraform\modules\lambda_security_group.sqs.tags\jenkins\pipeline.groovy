@Library("dostack-pipeline-lib@develop") _

def jfrog_image="base-image.dock.merck.com/dostack/jfrog-cli-v2/2.9.0/rel-2021w51"

pipeline {
    agent { label 'docker' && 'linux' }
    options {
        disableConcurrentBuilds()
        buildDiscarder(logRotator(numToKeepStr: '50', artifactNumToKeepStr: '50'))
        timeout(time: 1, unit: 'HOURS')
    }
    stages {
        stage('Skip ci'){    
            when {
                not {
                    anyOf {
                        changelog '.*\\[ci skip\\].*'; changelog '.*\\[skip ci\\].*'
                    }
                }
            }
            stages {
                stage('Gitleaks') {
                    steps {
                        gitleaksScan(
                            gitleaksExitCode: 0,
                            gitleaksConfigPath: '.gitleaks.toml'
                        )
                    }
                }
                stage("IaC TF Validate") {
                    steps {
                        iacTFValidate(
                            terraformCredentialsId: 'srviacpipeline',
                            terraformDockerImage: 'iac-shared.dock.merck.com/terraform/1.5.7:rel-2023w44.21'
                        )
                    }
                }
                stage("IaC Checkov") {
                    steps {
                        iacCheckov(
                            checkovConfigFile: '.checkov.yaml',
                            checkovCredentialsId: 'srviacpipeline',
                            checkovArchiveOutput: true
                        )
                    }
                }
                stage('Wiz') {
                    steps {
                        wizExec(
                            wizCredentialsId: 'srviacwiz'
                        )
                    }
                }
                stage('Infracost Estimation') {
                    steps {
                        iacCostExec()
                    }
                }
                stage('Upload module to Artifactory (main branch only)') {
                    when {
                        branch 'main'
                    }
                    agent {
                        docker {
                            image "${jfrog_image}"
                            args '-u 0:0 --entrypoint ""'
                            reuseNode true
                        }
                    }
                    steps {
                        withCredentials([usernamePassword(credentialsId: 'srviacpipeline', passwordVariable: 'SRVPASS', usernameVariable: 'SRVUSER')]) {
                            sh "./scripts/upload.sh"
                        }
                    }
                }
                stage('Trigger CD testing job (main branch only)') {
                    when {
                        expression {
                            String REPO_NAME=env.GIT_URL.tokenize('/').last().tokenize(".").first()
                            env.GIT_BRANCH == "main" && (REPO_NAME.endsWith("wrapper") || REPO_NAME.startsWith("iac-shared-tf-pattern"))
                        }
                    }
                    steps {
                        triggerCloudBeesPipeline(
                            cloudBeesCredentialsId: "srviacpipeline",
                            cloudBeesProjectName: "iac-shared",
                            cloudBeesConfigurationName: "CCD_Prod",
                            cloudBeesPipelineName: "iac-tf-module-testing"
                        )
                    }
                }
            }
        }
    }
}