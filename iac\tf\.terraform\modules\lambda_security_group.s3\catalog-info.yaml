apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: iac-shared-tf-module-aws-s3-bucket-wrapper
  description: Wrapper for S3 BUCKET
  namespace: merck-gen
  title: "IaC Shared Module: AWS S3 BUCKET Wrapper"
  annotations:
    github.com/team-slug: merck-gen/iac-shared
    github.com/project-slug: merck-gen/iac-shared-tf-module-aws-s3-bucket-wrapper
    backstage.io/techdocs-ref: dir:.
    jenkins.io/job-full-name: builds:GITHUB/iac-shared/iac-shared-tf-module-aws-s3-bucket-wrapper/multibranch-job/main
  tags:
    - terraform
    - aws
    - iac
    - wrapper
    - reusable
  links:
    - url: https://go.merck.com/iac
      title: IaC Shared Homepage
      icon: dashboard
    - url: https://go.merck.com/iacreleases
      title: IaC Shared Module Releases
      icon: dashboard
    - url: https://github.com/orgs/merck-gen/teams/iac-shared/repositories
      title: IaC Shared Github
      icon: code
spec:
  type: Library
  lifecycle: Production
  owner: group:merck-gen/iac-shared
  dependsOn:
    - Component:merck-gen/iac-shared-tf-module-aws-adoption-tags-int
    - Component:merck-gen/iac-shared-tf-module-aws-s3-bucket-ext
