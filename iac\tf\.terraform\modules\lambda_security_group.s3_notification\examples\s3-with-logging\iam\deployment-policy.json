{"Statement": [{"Action": ["dynamodb:DeleteItem", "dynamodb:GetItem", "dynamodb:PutItem", "iam:CreateRole", "iam:DeleteRole", "iam:GetRole", "iam:ListAttachedRolePolicies", "iam:ListInstanceProfilesForRole", "iam:ListRolePolicies", "iam:TagRole", "iam:UntagRole", "kms:C<PERSON><PERSON><PERSON>", "kms:DescribeKey", "kms:EnableKeyRotation", "kms:GetKeyPolicy", "kms:GetKeyRotationStatus", "kms:ListResourceTags", "kms:ScheduleKeyDeletion", "kms:TagResource", "kms:UntagResource", "s3:CreateBucket", "s3:DeleteBucket", "s3:DeleteBucketPolicy", "s3:GetAccelerateConfiguration", "s3:GetBucketAcl", "s3:GetBucketCORS", "s3:GetBucketLogging", "s3:GetBucketObjectLockConfiguration", "s3:GetBucketPolicy", "s3:GetBucketPublicAccessBlock", "s3:GetBucketRequestPayment", "s3:GetBucketTagging", "s3:GetBucketVersioning", "s3:GetBucketWebsite", "s3:GetEncryptionConfiguration", "s3:GetLifecycleConfiguration", "s3:GetObject", "s3:GetReplicationConfiguration", "s3:ListBucket", "s3:PutBucketLogging", "s3:PutBucketPolicy", "s3:PutBucketPublicAccessBlock", "s3:PutBucketTagging", "s3:PutBucketVersioning", "s3:PutEncryptionConfiguration", "s3:PutObject", "s3:TagResource", "s3:UntagResource", "sts:<PERSON><PERSON>Role", "sts:GetCallerIdentity"], "Effect": "Allow", "Resource": "*"}], "Version": "2012-10-17"}