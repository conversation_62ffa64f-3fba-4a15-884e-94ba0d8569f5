data "aws_partition" "current" {}
###############################
# Labels Module               #
###############################
module "labels" {
  source      = "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-labels-int_3.0.0.tgz"
  enabled     = true
  appname     = var.resource_vars.appname
  region      = var.resource_vars.region
  attributes  = var.resource_vars.attributes
  environment = var.resource_vars.environment
  label_order = var.resource_vars.label_order
}

data "aws_iam_policy_document" "sns_topic_policy" {
  statement {
    actions = [
      "SNS:SetTopicAttributes"
    ]

    condition {
      test     = "StringEquals"
      variable = "AWS:SourceOwner"

      values = [
        var.account_no,
      ]
    }

    effect = "Allow"

    principals {
      type        = "AWS"
      identifiers = ["*"]
    }

    sid = "allow_set_topic_attributes"
  }
}

###############################
# SNS Module                  #
###############################
module "sns_topic" {
  # Update this source to use the Artifactory URL of a released version of the module: https://go.merck.com/iacreleases
  source                        = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/sns/aws"
  version                       = "2.2.1"
  name                          = "${module.labels.id}-1"
  subscriptions                 = var.subscriptions
  default_tags                  = var.default_tags
  kms_master_key_id             = module.aws_kms_key.key_id
  depends_on                    = [module.aws_kms_key]
  source_topic_policy_documents = [data.aws_iam_policy_document.data_protection.json]
  topic_policy                  = data.aws_iam_policy_document.sns_topic_policy.json
  topic_policy_statements = {
    pub = {
      actions = ["sns:Publish"]
      principals = [{
        type        = "AWS"
        identifiers = ["arn:${var.partition}:iam::${var.account_no}:role/${var.deployment_role}"]
      }]
    }
  }
  # Controls whether default policy should be created for you
  enable_default_topic_policy = true
}

module "sns_other_topic" {
  # Update this source to use the Artifactory URL of a released version of the module: https://go.merck.com/iacreleases
  source                        = "artifacts.merck.com/terraform-iac-shared__terraform-aws-modules/sns/aws"
  version                       = "2.2.1"
  name                          = "${module.labels.id}-2"
  subscriptions                 = var.other_subscriptions
  default_tags                  = var.default_tags
  kms_master_key_id             = module.aws_kms_key.key_id
  depends_on                    = [module.aws_kms_key]
  source_topic_policy_documents = [data.aws_iam_policy_document.data_protection.json]
}

################################
# Supporting Resources         #
################################

########## KMS Key ###########
module "aws_kms_key" {
  source      = "https://artifacts.merck.com/artifactory/generic-iac-shared-local/terraform/releases/iac-shared-tf-module-aws-kms-wrapper_2.1.0.tgz"
  description = "Key for encrypting sns"
  key_usage   = "ENCRYPT_DECRYPT"
  # Policy
  key_statements = [
    {
      sid = "SNS"
      actions = [
        "kms:GenerateDataKey*",
        "kms:Decrypt"
      ]
      resources = ["*"]
      principals = [{
        type        = "Service"
        identifiers = ["sns.amazonaws.com"]
      }]
    }
  ]
  #policy = data.aws_iam_policy_document.kms_default.json
  #tags         = var.tags
  default_tags = var.default_tags
  aliases      = ["alias/${module.labels.id}"]
  depends_on   = [module.labels]
}

########## IAM Policy Document ###########
data "aws_iam_policy_document" "data_protection" {
  statement {
    sid       = "data_protection_policy1"
    actions   = ["sns:Publish"]
    effect    = "Deny"
    resources = ["arn:${data.aws_partition.current.partition}:sns:${var.region}:${var.account_no}:*"]
    condition {
      test     = "Bool"
      variable = "aws:SecureTransport"
      values   = ["false"]
    }
    principals {
      type        = "Service"
      identifiers = ["sns.amazonaws.com"]
    }
  }
}
