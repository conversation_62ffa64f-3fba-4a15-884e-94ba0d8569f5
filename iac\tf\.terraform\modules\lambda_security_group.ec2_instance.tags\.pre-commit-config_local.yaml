fail_fast: true
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.3.0
    hooks:
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: check-vcs-permalinks
      - id: end-of-file-fixer
      - id: trailing-whitespace
        args: [--markdown-linebreak-ext=md]
      - id: check-yaml
      - id: check-case-conflict
      - id: mixed-line-ending
        args: [--fix=lf]

  - repo: https://github.com/zricethezav/gitleaks
    rev: v8.13.0
    hooks:
      - id: gitleaks
        entry: gitleaks detect -c .gitleaks.toml --verbose --source=.

  - repo: https://github.com/antonbabenko/pre-commit-terraform
    rev: v1.76.0
    hooks:
      - id: terraform_fmt
      - id: terraform_validate
      - id: terraform_docs
      - id: terraform_tfsec
      - id: terraform_tflint
        args:
          - '--args=--only=terraform_deprecated_interpolation'
          - '--args=--only=terraform_deprecated_index'
          - '--args=--only=terraform_unused_declarations'
          - '--args=--only=terraform_comment_syntax'
          - '--args=--only=terraform_documented_outputs'
          - '--args=--only=terraform_documented_variables'
          - '--args=--only=terraform_typed_variables'
          - '--args=--only=terraform_module_pinned_source'
          - '--args=--only=terraform_naming_convention'
          - '--args=--only=terraform_required_version'
          - '--args=--only=terraform_required_providers'
          - '--args=--only=terraform_standard_module_structure'
          - '--args=--only=terraform_workspace_remote'
      - id: checkov
        args: [
            "-d", ".",
            "--config-file", ".checkov.yaml",
        ]

  - repo: https://github.com/trussworks/pre-commit-hooks.git
    rev: v1.1.1
    hooks:
      - id: gen-docs
        args: ["docs/ADRS"]
        types: ["file", "markdown"]
        pass_filenames: false
